// Application configuration management

// Validate required environment variables
function validateEnvVar(name: string, value: string | undefined): string {
  if (!value || value.trim() === '') {
    if (process.env.NODE_ENV === 'production') {
      throw new Error(`Missing required environment variable: ${name}`)
    }
    console.warn(`Warning: Missing environment variable: ${name}`)
    return ''
  }
  return value
}

export const config = {
  // Application settings
  app: {
    name: process.env.NEXT_PUBLIC_APP_NAME || 'بنتاكون',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0',
  },

  // Feature flags
  features: {
    analytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
    errorReporting: process.env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING === 'true',
    performanceMonitoring: process.env.NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING === 'true',
    debugMode: process.env.NEXT_PUBLIC_DEBUG_MODE === 'true',
    mockData: process.env.NEXT_PUBLIC_MOCK_DATA === 'true',
    multiTenant: process.env.NEXT_PUBLIC_MULTI_TENANT_MODE === 'true',
  },

  // Multi-tenant configuration
  tenant: {
    defaultTenantId: process.env.NEXT_PUBLIC_DEFAULT_TENANT_ID || '',
    defaultTenantSlug: process.env.NEXT_PUBLIC_DEFAULT_TENANT_SLUG || 'main',
    enableCustomDomains: process.env.NEXT_PUBLIC_ENABLE_CUSTOM_DOMAINS === 'true',
    enableSubdomains: process.env.NEXT_PUBLIC_ENABLE_SUBDOMAINS === 'true',
  },

  // Supabase configuration
  supabase: {
    url: validateEnvVar('NEXT_PUBLIC_SUPABASE_URL', process.env.NEXT_PUBLIC_SUPABASE_URL),
    anonKey: validateEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY),
    // Service role key is only needed on server-side
    serviceRoleKey: typeof window === 'undefined'
      ? validateEnvVar('SUPABASE_SERVICE_ROLE_KEY', process.env.SUPABASE_SERVICE_ROLE_KEY)
      : '', // Empty on client-side
  },

  // Database configuration
  database: {
    url: process.env.DATABASE_URL || '',
  },

  // Authentication configuration
  auth: {
    secret: process.env.NEXTAUTH_SECRET || '',
    url: process.env.NEXTAUTH_URL || '',
  },

  // Email configuration
  email: {
    host: process.env.SMTP_HOST || '',
    port: parseInt(process.env.SMTP_PORT || '587'),
    user: process.env.SMTP_USER || '',
    password: process.env.SMTP_PASSWORD || '',
  },

  // Payment configuration
  payment: {
    stripe: {
      publicKey: process.env.STRIPE_PUBLIC_KEY || '',
      secretKey: process.env.STRIPE_SECRET_KEY || '',
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
    },
  },

  // WhatsApp configuration
  whatsapp: {
    phoneNumber: process.env.WHATSAPP_PHONE_NUMBER || '',
    apiToken: process.env.WHATSAPP_API_TOKEN || '',
  },

  // File upload configuration
  upload: {
    maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '5242880'), // 5MB
    allowedTypes: (process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/webp').split(','),
  },

  // Rate limiting configuration
  rateLimit: {
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  },

  // Monitoring configuration
  monitoring: {
    sentryDsn: process.env.SENTRY_DSN || '',
    googleAnalyticsId: process.env.GOOGLE_ANALYTICS_ID || '',
  },

  // Redis configuration
  redis: {
    url: process.env.REDIS_URL || '',
  },

  // Security configuration
  security: {
    encryptionKey: process.env.ENCRYPTION_KEY || '',
    jwtSecret: process.env.JWT_SECRET || '',
  },
}

// Validation function to check required environment variables
export function validateConfig() {
  const errors: string[] = []

  // Check required variables for production
  if (config.app.environment === 'production') {
    if (!config.supabase.url) errors.push('NEXT_PUBLIC_SUPABASE_URL is required')
    if (!config.supabase.anonKey) errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY is required')
    if (!config.auth.secret) errors.push('NEXTAUTH_SECRET is required')
    if (!config.security.encryptionKey) errors.push('ENCRYPTION_KEY is required')
    if (!config.security.jwtSecret) errors.push('JWT_SECRET is required')
  }

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`)
  }

  return true
}

// Helper functions
export const isDevelopment = config.app.environment === 'development'
export const isProduction = config.app.environment === 'production'
export const isTest = config.app.environment === 'test'

// API endpoints configuration
export const apiEndpoints = {
  auth: {
    login: '/api/auth/login',
    logout: '/api/auth/logout',
    register: '/api/auth/register',
    refresh: '/api/auth/refresh',
  },
  products: {
    unified: '/api/admin/products/unified',
  },
  users: {
    list: '/api/users',
    create: '/api/users',
    update: (id: string) => `/api/users/${id}`,
    delete: (id: string) => `/api/users/${id}`,
  },
  orders: {
    list: '/api/orders',
    create: '/api/orders',
    update: (id: string) => `/api/orders/${id}`,
    delete: (id: string) => `/api/orders/${id}`,
  },
  upload: {
    image: '/api/upload/image',
    file: '/api/upload/file',
  },
}

// Cache configuration
export const cacheConfig = {
  // Cache durations in seconds
  durations: {
    products: 300, // 5 minutes
    users: 600, // 10 minutes
    orders: 60, // 1 minute
    homepage: 1800, // 30 minutes
    static: 86400, // 24 hours
  },
  
  // Cache keys
  keys: {
    products: 'products',
    users: 'users',
    orders: 'orders',
    homepage: 'homepage',
    banners: 'banners',
  },
}

// Performance thresholds
export const performanceThresholds = {
  // Core Web Vitals thresholds
  lcp: 2500, // Largest Contentful Paint (ms)
  fid: 100,  // First Input Delay (ms)
  cls: 0.1,  // Cumulative Layout Shift

  // Custom thresholds
  apiResponse: 1000, // API response time (ms)
  pageLoad: 3000,    // Page load time (ms)
  renderTime: 100,   // Component render time (ms)
}

// Security headers configuration
export const securityHeaders = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'origin-when-cross-origin',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'nonce-{NONCE}' https://cdn.jsdelivr.net",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "img-src 'self' data: https: blob:",
    "font-src 'self' https://fonts.gstatic.com",
    "connect-src 'self' https: wss:",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ].join('; '),
}

export default config
