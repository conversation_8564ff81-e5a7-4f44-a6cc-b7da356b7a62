import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../../lib/supabase/server'
import { z } from 'zod'

// Rate limiting map
const rateLimitMap = new Map()

function rateLimit(identifier: string, limit: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, [])
  }
  
  const requests = rateLimitMap.get(identifier)
  const validRequests = requests.filter((time: number) => time > windowStart)
  
  if (validRequests.length >= limit) {
    return false
  }
  
  validRequests.push(now)
  rateLimitMap.set(identifier, validRequests)
  
  return true
}

// Worker action schema
const workerActionSchema = z.object({
  action: z.enum(['accept', 'reject'], {
    errorMap: () => ({ message: 'Invalid action. Must be accept or reject' })
  }),
  reason: z.string().optional() // Optional reason for rejection
})

// POST /api/orders/[id]/worker-action - Admin or Worker accepts or rejects an order
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's tenant and verify admin or worker role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id, name')
      .eq('id', user.id)
      .single()

    if (!profile || (profile.role !== 'worker' && profile.role !== 'admin')) {
      return NextResponse.json({ error: 'Admin or worker access required' }, { status: 403 })
    }

    // Parse and validate request body
    const body = await request.json()
    const { action, reason } = workerActionSchema.parse(body)

    // Verify the order exists and belongs to the user's tenant
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, status, tenant_id, user_id, worker_id, worker_action, custom_data')
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (orderError || !order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 })
    }

    // Check if order is in pending status
    if (order.status !== 'pending') {
      return NextResponse.json({ 
        error: 'Order cannot be processed', 
        details: `Order status is ${order.status}` 
      }, { status: 400 })
    }

    // Check if order has already been processed by a worker
    if (order.worker_id && order.worker_action) {
      return NextResponse.json({ 
        error: 'Order has already been processed by another worker' 
      }, { status: 409 })
    }

    // Determine new order status based on action
    const newStatus = action === 'accept' ? 'completed' : 'failed'
    const workerAction = action === 'accept' ? 'accepted' : 'rejected'

    // Update order with worker action
    const { data: updatedOrder, error: updateError } = await supabase
      .from('orders')
      .update({
        status: newStatus,
        worker_id: user.id,
        worker_action: workerAction,
        worker_action_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // Add reason to custom_data if provided
        custom_data: {
          ...order.custom_data,
          ...(reason && { worker_reason: reason }),
          worker_name: profile.name
        }
      })
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .select()
      .single()

    if (updateError) {
      console.error('Worker action error:', updateError)
      return NextResponse.json({ 
        error: 'Failed to update order', 
        details: updateError.message 
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: `Order ${action}ed successfully`,
      order: {
        id: updatedOrder.id,
        status: updatedOrder.status,
        worker_action: updatedOrder.worker_action,
        worker_action_at: updatedOrder.worker_action_at,
        worker_name: profile.name
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error', 
        details: error.errors 
      }, { status: 400 })
    }
    
    console.error('Error in POST /api/orders/[id]/worker-action:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
