"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { useTenant } from './TenantContext'
import type { User } from '../types'
import type { User as SupabaseUser } from '@supabase/supabase-js'

// Simplified authentication types
export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
}

export type AuthModalType = 'login' | 'register' | 'forgot-password' | null

export interface AuthContextType {
  // State
  authState: AuthState
  currentModal: AuthModalType

  // Actions
  login: (email: string, password: string) => Promise<void>
  register: (email: string, password: string, name: string) => Promise<void>
  logout: () => Promise<void>
  resetPassword: (email: string) => Promise<void>

  // Modal controls
  openModal: (type: AuthModalType) => void
  closeModal: () => void

  // Utility functions
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { tenant } = useTenant()
  const [user, setUser] = useState<User | null>(null)
  const [currentModal, setCurrentModal] = useState<AuthModalType>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Try to get cached auth state to prevent flickering
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const cachedUser = localStorage.getItem('bentakon_auth_user')
        if (cachedUser) {
          const parsedUser = JSON.parse(cachedUser)
          // Only use cached user if it's recent (less than 5 minutes old)
          const cacheTime = localStorage.getItem('bentakon_auth_time')
          if (cacheTime && Date.now() - parseInt(cacheTime) < 5 * 60 * 1000) {
            setUser(parsedUser)
          }
        }
      } catch (error) {
        console.warn('Failed to load cached auth state:', error)
      }
    }
  }, [])

  // Auth state
  const authState: AuthState = {
    user,
    isLoading,
    isAuthenticated: !!user,
    error
  }

  // Clear error function
  const clearError = () => setError(null)

  // Simple auth initialization - no complexity
  useEffect(() => {
    let mounted = true

    const initAuth = async () => {
      try {
        // Get current session
        const { data: { session }, error } = await supabase.auth.getSession()

        console.log('Auth initialization:', {
          hasSession: !!session,
          hasUser: !!session?.user,
          userId: session?.user?.id,
          hasTenant: !!tenant,
          tenantId: tenant?.id,
          error: error?.message,
          timestamp: new Date().toISOString(),
          // Debug cookie info
          cookiesInBrowser: typeof document !== 'undefined' ? document.cookie : 'server-side',
          sessionAccessToken: session?.access_token ? 'present' : 'missing',
          sessionRefreshToken: session?.refresh_token ? 'present' : 'missing'
        })

        if (error) {
          console.error('Session error:', error)
          if (mounted) setIsLoading(false)
          return
        }

        // If we have session and tenant, load user profile
        if (session?.user && tenant && mounted) {
          const profile = await getUserProfile(session.user.id, tenant.id)
          console.log('User profile loaded:', {
            hasProfile: !!profile,
            profileId: profile?.id,
            role: profile?.role,
            tenantId: profile?.tenant_id
          })
          if (profile && mounted) {
            const userData = {
              id: profile.id,
              email: session.user.email || '',
              name: profile.name,
              role: profile.role,
              walletBalance: profile.wallet_balance || 0,
              avatar: profile.avatar,
              phone: profile.phone,
              createdAt: profile.created_at
            }
            setUser(userData)

            // Cache user data to prevent flickering on refresh
            if (typeof window !== 'undefined') {
              localStorage.setItem('bentakon_auth_user', JSON.stringify(userData))
              localStorage.setItem('bentakon_auth_time', Date.now().toString())
            }
          }
        }

        if (mounted) setIsLoading(false)
      } catch (error) {
        console.error('Auth init error:', error)
        if (mounted) {
          setError('فشل في تهيئة المصادقة')
          setIsLoading(false)
        }
      }
    }

    // Only initialize if we have tenant
    if (tenant) {
      initAuth()
    } else {
      // Reduce loading time when no tenant
      setTimeout(() => setIsLoading(false), 100)
    }

    return () => {
      mounted = false
    }
  }, [tenant])

  // Listen for auth changes
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user && tenant) {
          const profile = await getUserProfile(session.user.id, tenant.id)
          if (profile) {
            setUser({
              id: profile.id,
              email: session.user.email || '',
              name: profile.name,
              role: profile.role,
              walletBalance: profile.wallet_balance || 0,
              avatar: profile.avatar,
              phone: profile.phone,
              createdAt: profile.created_at
            })
          }
          setIsLoading(false)
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
          setIsLoading(false)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [tenant])

  // Helper function to get user profile
  const getUserProfile = async (userId: string, tenantId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .eq('tenant_id', tenantId)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Profile error:', error)
      return null
    }
  }

  // Simple login function
  const login = async (email: string, password: string): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      if (!tenant) {
        throw new Error('لم يتم تحديد المتجر')
      }

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) throw error
      setCurrentModal(null)
    } catch (error) {
      setIsLoading(false)
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء تسجيل الدخول'
      setError(errorMessage)
      throw error
    }
  }

  // Simple register function
  const register = async (email: string, password: string, name: string): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      if (!tenant) {
        throw new Error('لم يتم تحديد المتجر')
      }

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            tenant_id: tenant.id
          }
        }
      })

      if (error) throw error

      // Create user profile
      if (data.user) {
        await supabase.from('user_profiles').insert({
          id: data.user.id,
          name,
          email,
          role: 'user',
          tenant_id: tenant.id
        })
      }

      setCurrentModal(null)
    } catch (error) {
      setIsLoading(false)
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء الحساب'
      setError(errorMessage)
      throw error
    }
  }

  // Simple logout function
  const logout = async (): Promise<void> => {
    try {
      await supabase.auth.signOut()
      setUser(null)

      // Clear cached auth data
      if (typeof window !== 'undefined') {
        localStorage.removeItem('bentakon_auth_user')
        localStorage.removeItem('bentakon_auth_time')
      }
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  // Simple reset password function
  const resetPasswordFunction = async (email: string): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      const { error } = await supabase.auth.resetPasswordForEmail(email)
      if (error) throw error
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // Modal controls
  const openModal = (type: AuthModalType) => {
    clearError()
    setCurrentModal(type)
  }

  const closeModal = () => {
    clearError()
    setCurrentModal(null)
  }

  const value: AuthContextType = {
    authState,
    currentModal,
    login,
    register,
    logout,
    resetPassword: resetPasswordFunction,
    openModal,
    closeModal,
    clearError
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
