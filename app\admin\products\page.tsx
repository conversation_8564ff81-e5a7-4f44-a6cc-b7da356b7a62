import { Suspense } from 'react'
import { createClient } from '../../lib/supabase/server'
import { redirect } from 'next/navigation'
import ProductManagement from '../components/ProductManagement'
import type { Product, Category } from '../../types'

async function getProducts(): Promise<Product[]> {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    redirect('/auth/login')
  }

  // Get user's tenant and verify admin role
  const { data: profile } = await supabase
    .from('user_profiles')
    .select('role, tenant_id')
    .eq('id', user.id)
    .single()

  if (!profile || profile.role !== 'admin') {
    redirect('/dashboard')
  }

  // Fetch products
  const { data: products, error } = await supabase
    .from('products')
    .select(`
      id,
      tenant_id,
      title,
      slug,
      description,
      category_id,
      cover_image,
      tags,
      featured,
      original_price,
      user_price,
      discount_price,
      distributor_price,
      created_at,
      updated_at
    `)
    .eq('tenant_id', profile.tenant_id)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching products:', error)
    return []
  }

  // Return products data
  return products || []
}

async function getCategories(): Promise<Category[]> {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return []
  }

  // Get user's tenant
  const { data: profile } = await supabase
    .from('user_profiles')
    .select('tenant_id')
    .eq('id', user.id)
    .single()

  if (!profile) {
    return []
  }

  // Fetch categories
  const { data: categories, error } = await supabase
    .from('categories')
    .select('*')
    .eq('tenant_id', profile.tenant_id)
    .order('name', { ascending: true })

  if (error) {
    console.error('Error fetching categories:', error)
    return []
  }

  return categories || []
}

function ProductsLoading() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <div className="h-8 bg-gray-700 rounded w-48 mb-2"></div>
          <div className="h-4 bg-gray-700 rounded w-64"></div>
        </div>
        <div className="h-10 bg-gray-700 rounded w-32"></div>
      </div>

      {/* Filters Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="h-10 bg-gray-700 rounded"></div>
        ))}
      </div>

      {/* Products Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="bg-gray-800/30 border border-gray-700/50 rounded-lg overflow-hidden">
            <div className="h-48 bg-gray-700"></div>
            <div className="p-4 space-y-3">
              <div className="h-6 bg-gray-700 rounded w-3/4"></div>
              <div className="h-4 bg-gray-700 rounded w-full"></div>
              <div className="h-4 bg-gray-700 rounded w-2/3"></div>
              <div className="flex gap-2">
                <div className="h-8 bg-gray-700 rounded flex-1"></div>
                <div className="h-8 bg-gray-700 rounded w-12"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

async function ProductsContent() {
  const [products, categories] = await Promise.all([
    getProducts(),
    getCategories()
  ])

  return (
    <ProductManagement 
      initialProducts={products}
      categories={categories}
    />
  )
}

export default function ProductsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="container mx-auto px-4 py-8">
        <Suspense fallback={<ProductsLoading />}>
          <ProductsContent />
        </Suspense>
      </div>
    </div>
  )
}

export const metadata = {
  title: 'إدارة المنتجات - لوحة التحكم',
  description: 'إدارة منتجاتك وحزمها والحقول المخصصة'
}
