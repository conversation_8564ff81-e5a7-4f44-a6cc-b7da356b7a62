import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { config } from '../config'

export async function createClient() {
  const cookieStore = await cookies()

  return createServerClient(
    config.supabase.url,
    config.supabase.anonKey,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              // Ensure cookies persist in development
              const cookieOptions = {
                ...options,
                // Make cookies more persistent in development
                maxAge: process.env.NODE_ENV === 'development'
                  ? 60 * 60 * 24 * 7 // 7 days in development
                  : options?.maxAge,
                sameSite: 'lax' as const,
                secure: process.env.NODE_ENV === 'production'
              }
              cookieStore.set(name, value, cookieOptions)
            })
          } catch (error) {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
            if (process.env.NODE_ENV === 'development') {
              console.warn('Failed to set cookies in server component:', error)
            }
          }
        },
      },
      auth: {
        // Ensure session persistence
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true
      }
    }
  )
}
