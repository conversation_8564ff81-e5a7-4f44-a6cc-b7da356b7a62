import { createBrowserClient } from '@supabase/ssr'
import { config } from './config'

// Client-side Supabase client that syncs with cookies for SSR compatibility
export const supabase = createBrowserClient(
  config.supabase.url,
  config.supabase.anonKey,
  {
    cookies: {
      get(name: string) {
        if (typeof document !== 'undefined') {
          const value = document.cookie
            .split('; ')
            .find(row => row.startsWith(`${name}=`))
            ?.split('=')[1]
          return value ? decodeURIComponent(value) : undefined
        }
        return undefined
      },
      set(name: string, value: string, options: any) {
        if (typeof document !== 'undefined') {
          let cookieString = `${name}=${encodeURIComponent(value)}`

          if (options?.maxAge) {
            cookieString += `; max-age=${options.maxAge}`
          }
          if (options?.path) {
            cookieString += `; path=${options.path}`
          }
          if (options?.domain) {
            cookieString += `; domain=${options.domain}`
          }
          if (options?.secure) {
            cookieString += '; secure'
          }
          if (options?.sameSite) {
            cookieString += `; samesite=${options.sameSite}`
          }

          document.cookie = cookieString
        }
      },
      remove(name: string, options: any) {
        if (typeof document !== 'undefined') {
          let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`

          if (options?.path) {
            cookieString += `; path=${options.path}`
          }
          if (options?.domain) {
            cookieString += `; domain=${options.domain}`
          }

          document.cookie = cookieString
        }
      }
    }
  }
)

// Types for Supabase Auth
export interface AuthUser {
  id: string
  email?: string
  user_metadata: {
    name?: string
    avatar_url?: string
  }
  app_metadata: {
    role?: 'admin' | 'distributor' | 'user' | 'worker'
  }
}

// Helper function to get user profile data
export async function getUserProfile(userId: string) {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error fetching user profile:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      })
      return null
    }

    return data
  } catch (err) {
    console.error('Unexpected error fetching user profile:', err)
    return null
  }
}

// Helper function to create or update user profile
export async function upsertUserProfile(userId: string, profileData: {
  name: string
  role?: 'admin' | 'distributor' | 'user'
  wallet_balance?: number
  avatar?: string
  phone?: string
  tenant_id?: string
  email?: string
}) {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert({
        id: userId,
        ...profileData,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error upserting user profile:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      })
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (err) {
    console.error('Unexpected error upserting user profile:', err)
    return { success: false, error: 'حدث خطأ غير متوقع' }
  }
}

// Helper function to check if user is admin
export async function isUserAdmin(userId: string): Promise<boolean> {
  const profile = await getUserProfile(userId)
  return profile?.role === 'admin'
}

// Keep only essential helper functions - remove complexity

export default supabase
