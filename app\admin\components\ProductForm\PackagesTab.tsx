"use client"

import { useState } from 'react'
import { Plus, Trash2, Package as PackageIcon, DollarSign, Code, Image, Edit3, Eye, X, Package, Hash } from 'lucide-react'
import { calculateProfits, validatePricingRules } from '../../../lib/products'
import type { Package } from '../../../types'

interface PackageFormData {
  id?: string
  name: string
  description: string
  original_price: number
  user_price?: number
  discount_price?: number
  distributor_price?: number
  digital_codes: string
  image: string
  // Inventory fields
  manual_quantity?: number
  track_inventory?: boolean
  unlimited_stock?: boolean
  has_digital_codes?: boolean
}

interface PackagesTabProps {
  packages: PackageFormData[]
  onChange: (packages: PackageFormData[]) => void
  errors: Record<string, any>
  loading?: boolean
}

export default function PackagesTab({
  packages,
  onChange,
  errors,
  loading = false
}: PackagesTabProps) {
  const [showModal, setShowModal] = useState(false)
  const [editingPackage, setEditingPackage] = useState<Package | null>(null)
  const [digitalCodesInput, setDigitalCodesInput] = useState('')
  const [modalFormData, setModalFormData] = useState<PackageFormData>({
    name: '',
    description: '',
    original_price: 0,
    user_price: 0,
    discount_price: 0,
    distributor_price: 0,
    digital_codes: '',
    image: '',
    manual_quantity: 0,
    track_inventory: false,
    unlimited_stock: true,
    has_digital_codes: false
  })

  // Calculate quantity from digital codes
  const calculateCodesQuantity = (codes: string) => {
    if (!codes.trim()) return 0
    return codes.trim().split('\n').filter(line => line.trim()).length
  }

  // Handle digital codes input change
  const handleDigitalCodesChange = (codes: string) => {
    setDigitalCodesInput(codes)
    const quantity = calculateCodesQuantity(codes)
    const hasDigitalCodes = quantity > 0

    updateModalFormData({
      has_digital_codes: hasDigitalCodes,
      manual_quantity: hasDigitalCodes ? quantity : modalFormData.manual_quantity,
      track_inventory: hasDigitalCodes ? true : modalFormData.track_inventory,
      unlimited_stock: hasDigitalCodes ? false : modalFormData.unlimited_stock
    })
  }

  // Handle manual quantity vs unlimited stock mutual exclusivity
  const handleManualQuantityChange = (quantity: number) => {
    updateModalFormData({
      manual_quantity: quantity,
      unlimited_stock: quantity > 0 ? false : modalFormData.unlimited_stock,
      track_inventory: quantity > 0 ? true : false
    })
  }

  const handleUnlimitedStockChange = (unlimited: boolean) => {
    updateModalFormData({
      unlimited_stock: unlimited,
      manual_quantity: unlimited ? 0 : modalFormData.manual_quantity,
      track_inventory: unlimited ? false : (modalFormData.manual_quantity || 0) > 0
    })
  }
  const [modalNewFeature, setModalNewFeature] = useState('')
  const [modalErrors, setModalErrors] = useState<Record<string, string>>({})
  
  const openCreateModal = () => {
    setEditingPackage(null)
    setDigitalCodesInput('')
    setModalFormData({
      name: '',
      description: '',
      original_price: 0,
      user_price: 0,
      discount_price: 0,
      distributor_price: 0,
      digital_codes: '',
      image: '',
      manual_quantity: 0,
      track_inventory: false,
      unlimited_stock: true,
      has_digital_codes: false
    })
    setModalNewFeature('')
    setModalErrors({})
    setShowModal(true)
  }

  const openEditModal = (index: number) => {
    const pkg = packages[index]
    setEditingPackage({ ...pkg })
    setModalFormData({ ...pkg })
    // Set digital codes input from existing digital_codes field
    setDigitalCodesInput(pkg.digital_codes || '')
    setModalNewFeature('')
    setModalErrors({})
    setShowModal(true)
  }

  const closeModal = () => {
    setShowModal(false)
    setEditingPackage(null)
    setDigitalCodesInput('')
    setModalFormData({
      name: '',
      description: '',
      original_price: 0,
      user_price: 0,
      discount_price: 0,
      distributor_price: 0,
      digital_codes: '',
      image: '',
      manual_quantity: 0,
      track_inventory: false,
      unlimited_stock: true,
      has_digital_codes: false
    })
    setModalNewFeature('')
    setModalErrors({})
  }

  const updateModalFormData = (updates: Partial<PackageFormData>) => {
    setModalFormData(prev => ({ ...prev, ...updates }))
    // Clear related errors
    const newErrors = { ...modalErrors }
    Object.keys(updates).forEach(key => {
      delete newErrors[key]
    })
    setModalErrors(newErrors)
  }

  const validateModalForm = () => {
    const newErrors: Record<string, string> = {}

    if (!modalFormData.name.trim()) {
      newErrors.name = 'اسم الحزمة مطلوب'
    }

    if (modalFormData.original_price <= 0) {
      newErrors.original_price = 'السعر الأصلي يجب أن يكون أكبر من صفر'
    }

    setModalErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const saveModalPackage = () => {
    if (!validateModalForm()) return

    // Prepare the package data with digital codes from textarea
    const packageData = {
      ...modalFormData,
      digital_codes: digitalCodesInput.trim()
    }

    if (editingPackage !== null) {
      // Edit existing package
      const packageIndex = packages.findIndex(pkg =>
        JSON.stringify(pkg) === JSON.stringify(editingPackage)
      )
      if (packageIndex !== -1) {
        const newPackages = packages.map((pkg, i) =>
          i === packageIndex ? packageData : pkg
        )
        onChange(newPackages)
      }
    } else {
      // Add new package
      onChange([...packages, packageData])
    }

    closeModal()
  }

  const removePackage = (index: number) => {
    const newPackages = packages.filter((_, i) => i !== index)
    onChange(newPackages)
  }

  const updatePackage = (index: number, updates: Partial<PackageFormData>) => {
    const newPackages = packages.map((pkg, i) => 
      i === index ? { ...pkg, ...updates } : pkg
    )
    onChange(newPackages)
  }

  const getPackageErrors = (index: number) => {
    return errors.packages?.[index] || {}
  }

  const getPackageCalculations = (pkg: PackageFormData) => {
    if (!pkg.original_price || !pkg.user_price) return null
    
    try {
      return calculateProfits({
        original_price: pkg.original_price,
        user_price: pkg.user_price,
        discount_price: pkg.discount_price,
        distributor_price: pkg.distributor_price
      })
    } catch {
      return null
    }
  }

  const getPackageValidationErrors = (pkg: PackageFormData) => {
    if (!pkg.original_price || !pkg.user_price) return []
    
    const validation = validatePricingRules({
      original_price: pkg.original_price,
      user_price: pkg.user_price,
      discount_price: pkg.discount_price,
      distributor_price: pkg.distributor_price
    })
    
    return validation.errors
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium flex items-center gap-2">
            <PackageIcon className="w-5 h-5" />
            إدارة الحزم
          </h3>
          <p className="text-gray-400 text-sm mt-1">
            أضف حزم مختلفة للمنتج مع أسعار وأكواد رقمية منفصلة
          </p>
        </div>
        <button
          type="button"
          onClick={openCreateModal}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4" />
          إضافة حزمة
        </button>
      </div>

      {/* Packages List */}
      {packages.length === 0 ? (
        <div className="text-center py-12 border-2 border-dashed border-gray-600 rounded-lg">
          <PackageIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            لا توجد حزم
          </h3>
          <p className="text-gray-400 mb-4">
            أضف حزمة جديدة للبدء في إدارة المنتج بحزم منفصلة
          </p>
          <button
            type="button"
            onClick={openCreateModal}
            disabled={loading}
            className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            إضافة حزمة
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {packages.map((pkg, index) => {
            const calculations = getPackageCalculations(pkg)
            const validationErrors = getPackageValidationErrors(pkg)

            return (
              <div key={index} className="bg-gray-800/30 border border-gray-700 rounded-xl p-6 hover:border-gray-600 transition-colors">
                {/* Package Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-purple-600/20 rounded-lg flex items-center justify-center">
                      <PackageIcon className="w-5 h-5 text-purple-400" />
                    </div>
                    <div>
                      <h4 className="font-medium text-white">
                        {pkg.name || `الحزمة ${index + 1}`}
                      </h4>
                      <p className="text-sm text-gray-400">
                        {pkg.original_price > 0 && `${pkg.original_price} ر.س`}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Package Preview */}
                <div className="mb-4">
                  <div className="bg-gray-700/30 rounded-lg p-4">
                    {pkg.description && (
                      <p className="text-gray-300 text-sm mb-3">{pkg.description}</p>
                    )}

                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <span className="text-gray-400">السعر الأصلي:</span>
                        <p className="text-white font-medium">{pkg.original_price} ر.س</p>
                      </div>
                      <div>
                        <span className="text-gray-400">سعر المستخدم:</span>
                        <p className="text-white font-medium">{pkg.user_price} ر.س</p>
                      </div>
                      {calculations.profit > 0 && (
                        <div className="col-span-2">
                          <span className="text-gray-400">الربح:</span>
                          <p className="text-green-400 font-medium">{calculations.profit} ر.س</p>
                        </div>
                      )}
                    </div>

                    {pkg.digital_codes && (
                      <div className="mt-3 pt-3 border-t border-gray-600">
                        <span className="text-gray-400 text-xs">الأكواد الرقمية:</span>
                        <p className="text-gray-300 text-xs mt-1 truncate">{pkg.digital_codes}</p>
                      </div>
                    )}

                    {/* Inventory Information */}
                    <div className="mt-3 pt-3 border-t border-gray-600">
                      <span className="text-gray-400 text-xs">حالة المخزون:</span>
                      <div className="mt-1 space-y-1 text-xs">
                        {pkg.has_digital_codes ? (
                          <>
                            <div className="flex justify-between">
                              <span className="text-purple-400">✓ أكواد رقمية</span>
                              <span className="text-gray-300">{pkg.manual_quantity || 0} أكواد</span>
                            </div>
                          </>
                        ) : (
                          <>
                            {pkg.unlimited_stock ? (
                              <div className="text-green-400">✓ مخزون غير محدود</div>
                            ) : (
                              <>
                                <div className="flex justify-between">
                                  <span className="text-gray-400">الكمية:</span>
                                  <span className="text-white">{pkg.manual_quantity || 0} وحدة</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-400">الحالة:</span>
                                  <span className={pkg.manual_quantity && pkg.manual_quantity > 0 ? 'text-green-400' : 'text-red-400'}>
                                    {pkg.manual_quantity && pkg.manual_quantity > 0 ? '✓ متوفر' : '✗ غير متوفر'}
                                  </span>
                                </div>
                              </>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Validation Errors */}
                {validationErrors.length > 0 && (
                  <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                    <p className="text-red-400 text-xs font-medium mb-1">مشاكل في البيانات:</p>
                    {validationErrors.map((error, i) => (
                      <p key={i} className="text-red-400 text-xs">• {error}</p>
                    ))}
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <button
                    type="button"
                    onClick={() => openEditModal(index)}
                    disabled={loading}
                    className="flex items-center gap-2 px-3 py-2 text-purple-400 hover:text-purple-300 hover:bg-purple-500/10 rounded-lg transition-colors text-sm"
                  >
                    <Edit3 className="w-4 h-4" />
                    تعديل
                  </button>

                  <button
                    type="button"
                    onClick={() => removePackage(index)}
                    disabled={loading}
                    className="flex items-center gap-2 px-3 py-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors text-sm"
                  >
                    <Trash2 className="w-4 h-4" />
                    حذف
                  </button>
                </div>
              </div>
            )
          })}

        </div>
      )}

      {/* Package Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[60] flex items-center justify-center p-2 sm:p-4">
          <div className="bg-gray-900 rounded-2xl border border-gray-700 w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden shadow-2xl flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-600/20 rounded-lg flex items-center justify-center">
                  <PackageIcon className="w-5 h-5 text-purple-400" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-white">
                    {editingPackage ? 'تعديل الحزمة' : 'إضافة حزمة جديدة'}
                  </h2>
                  <p className="text-gray-400 text-sm">
                    قم بإنشاء حزمة بأسعار مختلفة وأكواد رقمية
                  </p>
                </div>
              </div>
              <button
                onClick={closeModal}
                disabled={loading}
                className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="flex-1 p-4 sm:p-6 overflow-y-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Form Fields */}
                <div className="space-y-6">
                  {/* Name */}
                  <div>
                    <label className="block text-sm font-medium mb-2 text-white">
                      اسم الحزمة <span className="text-red-400">*</span>
                    </label>
                    <input
                      type="text"
                      value={modalFormData.name}
                      onChange={(e) => updateModalFormData({ name: e.target.value })}
                      className={`w-full px-4 py-3 rounded-xl border ${
                        modalErrors.name
                          ? 'border-red-500 focus:border-red-500'
                          : 'border-gray-600 focus:border-purple-500'
                      } bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
                      placeholder="أدخل اسم الحزمة..."
                      disabled={loading}
                    />
                    {modalErrors.name && (
                      <p className="text-red-400 text-sm mt-1">{modalErrors.name}</p>
                    )}
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium mb-2 text-white">
                      وصف الحزمة
                    </label>
                    <textarea
                      value={modalFormData.description}
                      onChange={(e) => updateModalFormData({ description: e.target.value })}
                      rows={3}
                      className="w-full px-4 py-3 rounded-xl border border-gray-600 focus:border-purple-500 bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
                      placeholder="أدخل وصف الحزمة..."
                      disabled={loading}
                    />
                  </div>

                  {/* Pricing Grid */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {/* Original Price - Main Price */}
                    <div className="sm:col-span-2">
                      <label className="block text-sm font-medium mb-2 text-white">
                        السعر الأصلي <span className="text-red-400">*</span>
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          value={modalFormData.original_price || ''}
                          onChange={(e) => updateModalFormData({ original_price: parseFloat(e.target.value) || 0 })}
                          className={`w-full pl-9 pr-3 py-3 rounded-xl border ${
                            modalErrors.original_price
                              ? 'border-red-500 focus:border-red-500'
                              : 'border-gray-600 focus:border-purple-500'
                          } bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
                          placeholder="0.00"
                          disabled={loading}
                        />
                      </div>
                      <p className="text-gray-400 text-xs mt-1">السعر الأساسي للحزمة</p>
                      {modalErrors.original_price && (
                        <p className="text-red-400 text-sm mt-1">{modalErrors.original_price}</p>
                      )}
                    </div>

                    {/* User Price */}
                    <div>
                      <label className="block text-sm font-medium mb-2 text-white">
                        سعر المستخدم
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          value={modalFormData.user_price || ''}
                          onChange={(e) => updateModalFormData({ user_price: parseFloat(e.target.value) || 0 })}
                          className="w-full pl-9 pr-3 py-3 rounded-xl border border-gray-600 focus:border-purple-500 bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
                          placeholder="0.00"
                          disabled={loading}
                        />
                      </div>
                      <p className="text-gray-400 text-xs mt-1">السعر للعملاء العاديين</p>
                    </div>

                    {/* Discount Price */}
                    <div>
                      <label className="block text-sm font-medium mb-2 text-white">
                        سعر الخصم
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          value={modalFormData.discount_price || ''}
                          onChange={(e) => updateModalFormData({ discount_price: parseFloat(e.target.value) || 0 })}
                          className="w-full pl-9 pr-3 py-3 rounded-xl border border-gray-600 focus:border-purple-500 bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
                          placeholder="0.00"
                          disabled={loading}
                        />
                      </div>
                      <p className="text-gray-400 text-xs mt-1">السعر المخفض (اختياري)</p>
                    </div>

                    {/* Distributor Price */}
                    <div>
                      <label className="block text-sm font-medium mb-2 text-white">
                        سعر الموزع
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          value={modalFormData.distributor_price || ''}
                          onChange={(e) => updateModalFormData({ distributor_price: parseFloat(e.target.value) || 0 })}
                          className="w-full pl-9 pr-3 py-3 rounded-xl border border-gray-600 focus:border-purple-500 bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
                          placeholder="0.00"
                          disabled={loading}
                        />
                      </div>
                      <p className="text-gray-400 text-xs mt-1">سعر خاص للموزعين</p>
                    </div>
                  </div>

                  {/* Digital Codes */}
                  <div>
                    <label className="block text-sm font-medium mb-2 text-white flex items-center gap-2">
                      <Code className="w-4 h-4" />
                      الأكواد الرقمية
                    </label>
                    <textarea
                      value={modalFormData.digital_codes}
                      onChange={(e) => updateModalFormData({ digital_codes: e.target.value })}
                      rows={4}
                      className="w-full px-4 py-3 rounded-xl border border-gray-600 focus:border-purple-500 bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all resize-none font-mono text-sm"
                      placeholder="أدخل كود واحد في كل سطر..."
                      disabled={loading}
                    />
                    <p className="text-gray-400 text-xs mt-1">
                      أدخل كود رقمي واحد في كل سطر. سيتم توزيع الأكواد تلقائياً على الطلبات.
                    </p>
                  </div>

                  {/* Image */}
                  <div>
                    <label className="block text-sm font-medium mb-2 text-white flex items-center gap-2">
                      <Image className="w-4 h-4" />
                      صورة الحزمة
                    </label>
                    <input
                      type="url"
                      value={modalFormData.image}
                      onChange={(e) => updateModalFormData({ image: e.target.value })}
                      className="w-full px-4 py-3 rounded-xl border border-gray-600 focus:border-purple-500 bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
                      placeholder="https://example.com/package-image.jpg"
                      disabled={loading}
                    />
                  </div>

                  {/* Inventory Management */}
                  <div className="bg-gray-800/20 rounded-xl p-4 border border-gray-700">
                    <h4 className="text-sm font-medium text-white mb-4 flex items-center gap-2">
                      <Package className="w-4 h-4" />
                      إدارة المخزون
                    </h4>

                    {/* Digital Codes Section */}
                    <div className="mb-6">
                      <div className="bg-purple-900/20 rounded-lg p-4 border border-purple-500/30">
                        <div className="flex items-center gap-2 mb-3">
                          <Code className="w-4 h-4 text-purple-400" />
                          <h5 className="text-sm font-medium text-white">أكواد رقمية</h5>
                        </div>

                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium mb-2 text-gray-200">
                              أدخل الأكواد الرقمية
                            </label>
                            <textarea
                              value={digitalCodesInput}
                              onChange={(e) => handleDigitalCodesChange(e.target.value)}
                              placeholder="أدخل كل كود في سطر منفصل..."
                              rows={4}
                              className="w-full px-3 py-2 rounded-lg border border-gray-600 focus:border-purple-500 bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all resize-none font-mono text-sm"
                              disabled={loading}
                            />
                            <p className="text-gray-400 text-xs mt-1">
                              أدخل كل كود رقمي في سطر منفصل
                            </p>
                          </div>

                          {digitalCodesInput.trim() && (
                            <div className="bg-gray-700/30 rounded-lg p-2">
                              <div className="flex items-center justify-between text-xs">
                                <span className="text-gray-300">الكمية:</span>
                                <span className="text-purple-400 font-medium">
                                  {calculateCodesQuantity(digitalCodesInput)} أكواد
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Non-Digital Products Section */}
                    {!modalFormData.has_digital_codes && (
                      <div className="bg-blue-900/20 rounded-lg p-4 border border-blue-500/30">
                        <div className="flex items-center gap-2 mb-3">
                          <Hash className="w-4 h-4 text-blue-400" />
                          <h5 className="text-sm font-medium text-white">منتج عادي</h5>
                        </div>

                        <div className="space-y-3">
                          {/* Manual Quantity */}
                          <div>
                            <label className="block text-sm font-medium mb-2 text-gray-200">
                              عدد الوحدات المتاحة
                            </label>
                            <input
                              type="number"
                              min="0"
                              value={modalFormData.manual_quantity || ''}
                              onChange={(e) => handleManualQuantityChange(parseInt(e.target.value) || 0)}
                              className="w-full px-3 py-2 rounded-lg border border-gray-600 focus:border-blue-500 bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 transition-all"
                              placeholder="0"
                              disabled={loading || modalFormData.unlimited_stock}
                            />
                          </div>

                          {/* Unlimited Stock Checkbox */}
                          <div>
                            <label className="flex items-center gap-2 cursor-pointer">
                              <input
                                type="checkbox"
                                checked={modalFormData.unlimited_stock || false}
                                onChange={(e) => handleUnlimitedStockChange(e.target.checked)}
                                className="w-4 h-4 rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500 focus:ring-2"
                                disabled={loading}
                              />
                              <div>
                                <span className="text-sm font-medium text-white">مخزون غير محدود</span>
                                <p className="text-xs text-gray-400">الحزمة متاحة دائماً للبيع</p>
                              </div>
                            </label>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Preview */}
                <div className="space-y-6">
                  <div className="bg-gray-800/30 rounded-xl p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Eye className="w-5 h-5 text-purple-400" />
                      <h3 className="text-lg font-medium text-white">معاينة الحزمة</h3>
                    </div>

                    <div className="bg-gray-700/30 rounded-xl p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-8 h-8 bg-purple-600/20 rounded-lg flex items-center justify-center">
                          <PackageIcon className="w-4 h-4 text-purple-400" />
                        </div>
                        <div>
                          <h4 className="font-medium text-white">
                            {modalFormData.name || 'اسم الحزمة'}
                          </h4>
                          <p className="text-sm text-gray-400">
                            {modalFormData.original_price > 0 && `${modalFormData.original_price} ر.س`}
                          </p>
                        </div>
                      </div>

                      {modalFormData.description && (
                        <p className="text-gray-300 text-sm mb-3">{modalFormData.description}</p>
                      )}

                      <div className="grid grid-cols-2 gap-3 text-sm mb-3">
                        <div>
                          <span className="text-gray-400">السعر الأصلي:</span>
                          <p className="text-white font-medium">{modalFormData.original_price || 0} ر.س</p>
                        </div>
                        <div>
                          <span className="text-gray-400">سعر المستخدم:</span>
                          <p className="text-white font-medium">{modalFormData.user_price || 0} ر.س</p>
                        </div>
                      </div>

                      {modalFormData.digital_codes && (
                        <div className="pt-3 border-t border-gray-600">
                          <span className="text-gray-400 text-xs">الأكواد الرقمية:</span>
                          <p className="text-gray-300 text-xs mt-1 font-mono">
                            {modalFormData.digital_codes.split('\n').length} كود متاح
                          </p>
                        </div>
                      )}

                      {/* Inventory Preview */}
                      <div className="pt-3 border-t border-gray-600">
                        <span className="text-gray-400 text-xs">حالة المخزون:</span>
                        <div className="mt-1 space-y-1">
                          {modalFormData.has_digital_codes ? (
                            <>
                              <p className="text-purple-400 text-xs">✓ أكواد رقمية</p>
                              <p className="text-gray-300 text-xs">
                                الكمية: {calculateCodesQuantity(digitalCodesInput)} أكواد
                              </p>
                            </>
                          ) : (
                            <>
                              {modalFormData.unlimited_stock ? (
                                <p className="text-green-400 text-xs">✓ مخزون غير محدود</p>
                              ) : (
                                <>
                                  <p className="text-gray-300 text-xs">
                                    الكمية: {modalFormData.manual_quantity || 0} وحدة
                                  </p>
                                  <p className={`text-xs ${modalFormData.manual_quantity && modalFormData.manual_quantity > 0 ? 'text-green-400' : 'text-red-400'}`}>
                                    {modalFormData.manual_quantity && modalFormData.manual_quantity > 0 ? '✓ متوفر' : '✗ غير متوفر'}
                                  </p>
                                </>
                              )}
                            </>
                          )}
                        </div>
                      </div>

                      {modalFormData.image && (
                        <div className="mt-3 pt-3 border-t border-gray-600">
                          <img
                            src={modalFormData.image}
                            alt="معاينة صورة الحزمة"
                            className="w-full h-24 object-cover rounded-lg"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement
                              target.style.display = 'none'
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-3 p-4 sm:p-6 border-t border-gray-700 bg-gray-900/50 shrink-0">
              <button
                onClick={closeModal}
                disabled={loading}
                className="w-full sm:w-auto px-6 py-3 text-gray-400 hover:text-white hover:bg-gray-800 rounded-xl transition-colors order-2 sm:order-1"
              >
                إلغاء
              </button>
              <button
                onClick={saveModalPackage}
                disabled={loading}
                className="w-full sm:w-auto px-6 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-xl transition-colors flex items-center justify-center gap-2 order-1 sm:order-2"
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                    جاري الحفظ...
                  </>
                ) : (
                  <>
                    <PackageIcon className="w-4 h-4" />
                    {editingPackage ? 'تحديث الحزمة' : 'إضافة الحزمة'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
