import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../lib/supabase/server'
import { z } from 'zod'
import { rateLimit } from '../../../lib/rate-limit'
import { 
  productCreateSchema, 
  productUpdateSchema, 
  generateSlug,
  validatePricingRules 
} from '../../../lib/products'


// GET /api/admin/products - Get products for current tenant
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = await createClient()

    // Try to get authenticated user's tenant first
    let tenantId = request.headers.get('x-tenant-id')

    const { data: { user } } = await supabase.auth.getUser()
    if (user) {
      // Get user's tenant
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('tenant_id')
        .eq('id', user.id)
        .single()

      if (profile?.tenant_id) {
        tenantId = profile.tenant_id
      }
    }

    // Fallback to main tenant if no user or tenant found
    if (!tenantId) {
      const { data: mainTenant } = await supabase
        .from('tenants')
        .select('id')
        .eq('slug', 'main')
        .single()

      tenantId = mainTenant?.id || process.env.DEFAULT_TENANT_ID || 'default-tenant'
    }

    console.log('GET products - tenantId:', tenantId, 'user:', user?.id)

    // Parse query parameters
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '20'), 100)
    const search = url.searchParams.get('search') || ''
    const category = url.searchParams.get('category') || ''
    const featured = url.searchParams.get('featured')
    
    const offset = (page - 1) * limit

    // Build query
    let query = supabase
      .from('products')
      .select(`
        id,
        tenant_id,
        title,
        slug,
        description,
        category_id,
        cover_image,
        tags,
        featured,
        original_price,
        user_price,
        discount_price,
        distributor_price,
        created_at,
        updated_at
      `)
      .eq('tenant_id', tenantId)

    // Apply filters
    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`)
    }
    
    if (category) {
      query = query.eq('category_id', category)
    }
    
    if (featured === 'true') {
      query = query.eq('featured', true)
    } else if (featured === 'false') {
      query = query.eq('featured', false)
    }

    // Get total count
    const { count } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenantId)

    // Get paginated results
    const { data: products, error } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    console.log('Products query result:', { products: products?.length, error, tenantId })

    if (error) {
      console.error('Database error fetching products:', error)
      return NextResponse.json({
        success: false,
        error: 'فشل في جلب المنتجات',
        details: error.message
      }, { status: 500 })
    }

    // Return products as-is
    const processedProducts = products || []

    return NextResponse.json({
      success: true,
      data: processedProducts,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })

  } catch (error) {
    console.error('Error in products GET:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}

// POST /api/admin/products - Create new product
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'طلبات كثيرة جداً' }, { status: 429 })
    }

    const supabase = await createClient()

    // Debug cookies
    const { cookies } = await import('next/headers')
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    const authCookies = allCookies.filter(c => c.name.includes('supabase'))
    console.log('Available auth cookies:', authCookies.map(c => ({ name: c.name, hasValue: !!c.value })))

    const { data: { user }, error: authError } = await supabase.auth.getUser()

    console.log('Auth check:', { user: user?.id, authError })

    if (!user) {
      console.log('No user found in session')
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    console.log('Profile check:', { profile, profileError })

    if (!profile || profile.role !== 'admin') {
      console.log('User not admin:', { profile })
      return NextResponse.json({ error: 'غير مصرح - مطلوب صلاحيات إدارية' }, { status: 403 })
    }

    const body = await request.json()
    
    // Validate input
    const validation = productCreateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        success: false,
        error: 'بيانات غير صالحة',
        details: validation.error.errors.map(e => e.message)
      }, { status: 400 })
    }

    const validatedData = validation.data

    // Auto-generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = generateSlug(validatedData.title)
    }

    // Validate pricing if provided
    if (validatedData.original_price && validatedData.user_price) {
      const pricingValidation = validatePricingRules({
        original_price: validatedData.original_price,
        user_price: validatedData.user_price,
        discount_price: validatedData.discount_price,
        distributor_price: validatedData.distributor_price
      })

      if (!pricingValidation.valid) {
        return NextResponse.json({
          success: false,
          error: 'أخطاء في التسعير',
          details: pricingValidation.errors
        }, { status: 400 })
      }
    }

    // Check for duplicate slug in tenant
    const { data: existingProduct } = await supabase
      .from('products')
      .select('id')
      .eq('tenant_id', profile.tenant_id)
      .eq('slug', validatedData.slug)
      .single()

    if (existingProduct) {
      return NextResponse.json({ 
        success: false,
        error: 'الرابط المختصر موجود بالفعل' 
      }, { status: 409 })
    }

    // Verify category exists
    const { data: category } = await supabase
      .from('categories')
      .select('id')
      .eq('id', validatedData.category_id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!category) {
      return NextResponse.json({ 
        success: false,
        error: 'الفئة المحددة غير موجودة' 
      }, { status: 400 })
    }

    // Insert new product
    const { data: product, error } = await supabase
      .from('products')
      .insert({
        ...validatedData,
        tenant_id: profile.tenant_id,
        featured: validatedData.featured || false
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating product:', error)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في إنشاء المنتج' 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      data: product 
    }, { status: 201 })

  } catch (error) {
    console.error('Error in products POST:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}
