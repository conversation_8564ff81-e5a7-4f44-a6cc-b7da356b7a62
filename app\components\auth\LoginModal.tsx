"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { useAuth } from '../../contexts/AuthContext'
import { useTenant } from '../../contexts/TenantContext'
import { Eye, EyeOff, Mail, Lock, Loader2, X } from 'lucide-react'
import { toast } from 'sonner'

interface LoginModalProps {
  isOpen: boolean
  onClose: () => void
  onSwitchToRegister: () => void
  onSwitchToForgotPassword: () => void
}

export default function LoginModal({ 
  isOpen, 
  onClose, 
  onSwitchToRegister, 
  onSwitchToForgotPassword 
}: LoginModalProps) {
  const { login, authState, clearError } = useAuth()
  const { tenant } = useTenant()
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.email || !formData.password) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    try {
      await login(formData.email, formData.password)
      toast.success('تم تسجيل الدخول بنجاح!')
      onClose()
    } catch (error) {
      // Error is already set in auth context
      toast.error(authState.error || 'حدث خطأ أثناء تسجيل الدخول')
    }
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (authState.error) {
      clearError()
    }
  }

  const handleClose = () => {
    clearError()
    setFormData({ email: '', password: '', rememberMe: false })
    setShowPassword(false)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md mx-auto bg-gray-900/95 backdrop-blur-md border border-gray-700/50 shadow-2xl">
        {/* Custom Header with Close Button */}
        <div className="flex items-center justify-between p-6 pb-4">
          <div>
            <DialogTitle className="text-xl font-bold text-white">
              تسجيل الدخول
            </DialogTitle>
            {tenant && (
              <p className="text-sm text-gray-400 mt-1">
                إلى {tenant.name}
              </p>
            )}
          </div>
          <button
            onClick={handleClose}
            className="p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200"
            aria-label="إغلاق"
          >
            <X className="w-5 h-5 text-gray-400 hover:text-white" />
          </button>
        </div>

        <div className="px-6 pb-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium text-gray-200">
                البريد الإلكتروني
              </Label>
              <div className="relative">
                <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="أدخل بريدك الإلكتروني"
                  className="pr-10 bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400 focus:border-purple-500/50 focus:ring-purple-500/50"
                  disabled={authState.isLoading}
                  required
                />
              </div>
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium text-gray-200">
                كلمة المرور
              </Label>
              <div className="relative">
                <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder="أدخل كلمة المرور"
                  className="px-10 bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400 focus:border-purple-500/50 focus:ring-purple-500/50"
                  disabled={authState.isLoading}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors duration-200"
                  disabled={authState.isLoading}
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="rememberMe"
                  checked={formData.rememberMe}
                  onCheckedChange={(checked) => handleInputChange('rememberMe', !!checked)}
                  disabled={authState.isLoading}
                  className="border-gray-600 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                />
                <Label htmlFor="rememberMe" className="text-sm text-gray-300 cursor-pointer">
                  تذكرني
                </Label>
              </div>
              <button
                type="button"
                onClick={onSwitchToForgotPassword}
                className="text-sm text-purple-400 hover:text-purple-300 transition-colors duration-200"
                disabled={authState.isLoading}
              >
                نسيت كلمة المرور؟
              </button>
            </div>

            {/* Error Message */}
            {authState.error && (
              <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm">
                {authState.error}
              </div>
            )}

            {/* Login Button */}
            <Button
              type="submit"
              disabled={authState.isLoading}
              className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {authState.isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                  جاري تسجيل الدخول...
                </>
              ) : (
                'تسجيل الدخول'
              )}
            </Button>

            {/* Register Link */}
            <div className="text-center pt-4 border-t border-gray-700/50">
              <p className="text-sm text-gray-400">
                ليس لديك حساب؟{' '}
                <button
                  type="button"
                  onClick={onSwitchToRegister}
                  className="text-purple-400 hover:text-purple-300 font-medium transition-colors duration-200"
                  disabled={authState.isLoading}
                >
                  إنشاء حساب جديد
                </button>
              </p>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  )
}
