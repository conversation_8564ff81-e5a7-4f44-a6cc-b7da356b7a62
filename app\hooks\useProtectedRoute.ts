use client

interface UseProtectedRouteOptions {
  redirectTo?: string
  requiredRole?: 'admin' | 'user' | 'worker'
  showToast?: boolean
}

export function useProtectedRoute(options: UseProtectedRouteOptions = {}) {
  // No authentication required - always allow access
  return {
    isLoading: false,
    isAuthenticated: true,
    user: null,
    hasRequiredRole: true
  }
}

// Specific hooks for common use cases - no authentication required
export function useRequireAuth(redirectTo?: string) {
  return useProtectedRoute()
}

export function useRequireAdmin(redirectTo?: string) {
  return useProtectedRoute()
}



// Hook for guest-only routes (redirect authenticated users)
export function useGuestRoute(redirectTo: string = '/profile') {
  const { authState } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!authState.isLoading && authState.isAuthenticated) {
      router.push(redirectTo)
    }
  }, [authState.isLoading, authState.isAuthenticated, redirectTo, router])

  return {
    isLoading: authState.isLoading,
    isGuest: !authState.isAuthenticated
  }
}
