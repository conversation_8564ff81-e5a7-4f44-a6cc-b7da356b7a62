"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { Users, Package, BarChart3, DollarSign, ShoppingCart, Home, Menu, X, Tag, Coins, TrendingUp } from "lucide-react"
import {
  LazyUserManagement,
  LazyOrderManagement
} from "../components/LazyComponents"
import HomepageManagement from "./components/HomepageManagement"
import CategoryManagement from "./components/CategoryManagement"
import ProductManagement from "./components/ProductManagement"
import { convertAndFormatPrice } from "../utils/currency"
import { useData } from "../contexts/DataContext"
import { useAuth } from "../contexts/AuthContext"
// import { calculateTotalEarnings, calculateEarningsByPeriod, getTopProfitableProducts, getEarningsByCurrency, formatEarningsData } from "../utils/earnings" // REMOVED: Product management system has been removed
// import { Money } from "../components/Money"
import dynamic from "next/dynamic"

// Lazy load the currencies component
const CurrenciesAdmin = dynamic(() => import('./currencies/page'), {
  loading: () => <div className="p-6">جاري تحميل إدارة العملات...</div>
})

export default function AdminDashboard() {
  // Use authentication context
  const { authState } = useAuth()

  // Use centralized data context
  const { products: dataContextProducts, users, orders } = useData() // Check if products come from DataContext

  // Router for URL management
  const router = useRouter()
  const searchParams = useSearchParams()

  // Get tab from URL or set default
  const getInitialTab = () => {
    const tabFromUrl = searchParams.get('tab')
    if (tabFromUrl) return tabFromUrl
    return "overview"
  }

  const [activeTab, setActiveTab] = useState(getInitialTab())
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [products, setProducts] = useState([])
  const [categories, setCategories] = useState([])
  const [productsLoading, setProductsLoading] = useState(false)

  // Define fetchProductsData function before useEffect
  const fetchProductsData = async () => {
    console.log('fetchProductsData called')

    // Check if authentication is ready
    if (authState.isLoading) {
      console.log('Authentication still loading, skipping API calls')
      return
    }

    if (!authState.isAuthenticated) {
      console.log('User not authenticated, skipping API calls')
      return
    }

    setProductsLoading(true)
    try {
      // Fetch products
      try {
        console.log('Fetching products from /api/admin/products...')
        const productsResponse = await fetch('/api/admin/products')
        console.log('Products response status:', productsResponse.status)
        if (productsResponse.ok) {
          const productsData = await productsResponse.json()
          console.log('Products API response:', productsData)
          setProducts(productsData.data || [])
        } else {
          console.error('Products API error:', productsResponse.status, await productsResponse.text())
        }
      } catch (error) {
        console.error('Products fetch exception:', error)
      }

      // Fetch categories
      const categoriesResponse = await fetch('/api/categories')
      if (categoriesResponse.ok) {
        const categoriesData = await categoriesResponse.json()
        console.log('Categories API response:', categoriesData)
        setCategories(categoriesData.categories || [])
      } else {
        console.error('Categories API error:', categoriesResponse.status, await categoriesResponse.text())
      }

      console.log('Final products state:', products.length)
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setProductsLoading(false)
    }
  }

  // Debug DataContext products
  useEffect(() => {
    console.log('DataContext products:', dataContextProducts?.length)
  }, [dataContextProducts])

  // Sync activeTab with URL changes (browser back/forward)
  useEffect(() => {
    const tabFromUrl = searchParams.get('tab')
    if (tabFromUrl && tabFromUrl !== activeTab) {
      setActiveTab(tabFromUrl)
    }
  }, [searchParams, activeTab])

  // Fetch products and categories when products or homepage tab is active and auth is ready
  useEffect(() => {
    console.log('Tab changed to:', activeTab)
    console.log('Auth state:', { isLoading: authState.isLoading, isAuthenticated: authState.isAuthenticated })

    if ((activeTab === 'products' || activeTab === 'homepage') && !authState.isLoading && authState.isAuthenticated) {
      console.log('Triggering fetchProductsData for tab:', activeTab)
      fetchProductsData()
    }
  }, [activeTab, authState.isLoading, authState.isAuthenticated])

  // Show loading state while authentication is initializing
  if (authState.isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-300">جاري تحميل لوحة التحكم...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!authState.isAuthenticated) {
    router.push('/auth/login')
    return null
  }


  // Define all available tabs with their routes
  const allTabs = [
    { id: "overview", label: "نظرة عامة", icon: BarChart3, adminOnly: true, route: "/admin?tab=overview" },
    { id: "products", label: "المنتجات", icon: Package, adminOnly: true, route: "/admin?tab=products" },
    { id: "categories", label: "الفئات", icon: Tag, adminOnly: true, route: "/admin?tab=categories" },
    { id: "currencies", label: "العملات", icon: Coins, adminOnly: true, route: "/admin?tab=currencies" },
    { id: "users", label: "المستخدمون", icon: Users, adminOnly: true, route: "/admin?tab=users" },
    { id: "orders", label: "الطلبات", icon: ShoppingCart, adminOnly: false, route: "/admin?tab=orders" },
    { id: "homepage", label: "الصفحة الرئيسية", icon: Home, adminOnly: true, route: "/admin?tab=homepage" },
  ]

  // Show all tabs (no role filtering)
  const tabs = allTabs

  // Function to handle tab changes with URL updates
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId)
    // Update URL without page reload
    const newUrl = `/admin?tab=${tabId}`
    router.push(newUrl, { scroll: false })
  }







  // No authentication checks - direct access to admin dashboard

  // Calculate earnings data - REMOVED: Product management system has been removed
  // const allPackages = products.flatMap(p => p.packages || [])
  // const earningsData = calculateTotalEarnings(orders, products, allPackages)
  // const monthlyEarnings = calculateEarningsByPeriod(orders, products, allPackages, 30)
  // const topProducts = getTopProfitableProducts(orders, products, allPackages, 3)
  // const currencyBreakdown = getEarningsByCurrency(orders, products, allPackages)

  const stats = {
    totalProducts: 0, // REMOVED: Product management system has been removed
    totalUsers: users.length,
    totalOrders: orders.length,
    totalRevenue: 0, // REMOVED: Product management system has been removed
    totalProfit: 0, // REMOVED: Product management system has been removed
    profitMargin: 0, // REMOVED: Product management system has been removed
    pendingOrders: orders.filter((o) => o.status === "pending").length,
    completedOrders: orders.filter((o) => o.status === "completed").length,
    monthlyProfit: 0, // REMOVED: Product management system has been removed
    monthlyRevenue: 0, // REMOVED: Product management system has been removed
  }

  return (
    <div className="container mx-auto px-4 py-4 md:py-8">
      {/* Header */}
      <div className="mb-6 md:mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-2 md:mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              لوحة التحكم الإدارية
            </h1>
            <p className="text-gray-400 text-sm md:text-lg">إدارة متجرك والمنتجات والمستخدمين والصفحة الرئيسية</p>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="lg:hidden p-2 rounded-xl bg-gray-800/50 backdrop-blur-md border border-gray-700/50 text-gray-400 hover:text-white transition-colors"
            aria-label="فتح القائمة"
          >
            {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>
      </div>

      <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6 lg:gap-8">
        {/* Mobile Navigation Overlay */}
        {isMobileMenuOpen && (
          <div className="lg:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm" onClick={() => setIsMobileMenuOpen(false)}>
            <div className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-gray-900/95 backdrop-blur-md border-l border-gray-700/50 shadow-2xl">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-white">القائمة</h2>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-gray-700/50 transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                <nav className="space-y-3">
                  {tabs.map((tab) => {
                    const Icon = tab.icon
                    return (
                      <button
                        key={tab.id}
                        onClick={() => {
                          handleTabChange(tab.id)
                          setIsMobileMenuOpen(false)
                        }}
                        className={`w-full flex items-center space-x-3 space-x-reverse px-4 py-4 rounded-xl transition-all duration-300 ${
                          activeTab === tab.id
                            ? "bg-purple-600 text-white shadow-lg shadow-purple-500/25"
                            : "text-gray-400 hover:text-white hover:bg-gray-700/50"
                        }`}
                      >
                        <Icon className="w-6 h-6" />
                        <span className="text-lg">{tab.label}</span>
                      </button>
                    )
                  })}
                </nav>
              </div>
            </div>
          </div>
        )}

        {/* Desktop Sidebar Navigation */}
        <div className="hidden lg:block lg:col-span-1">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 p-4 shadow-xl sticky top-6">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => handleTabChange(tab.id)}
                    className={`w-full flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-xl transition-all duration-300 ${
                      activeTab === tab.id
                        ? "bg-purple-600 text-white shadow-lg shadow-purple-500/25"
                        : "text-gray-400 hover:text-white hover:bg-gray-700/50"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.label}</span>
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Mobile Tab Bar */}
        <div className="lg:hidden order-last">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 p-2 shadow-xl">
            <div className="grid grid-cols-4 gap-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => handleTabChange(tab.id)}
                    className={`flex flex-col items-center space-y-1 px-2 py-3 rounded-lg transition-all duration-300 ${
                      activeTab === tab.id
                        ? "bg-purple-600 text-white shadow-lg shadow-purple-500/25"
                        : "text-gray-400 hover:text-white hover:bg-gray-700/50"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="text-xs font-medium">{tab.label}</span>
                  </button>
                )
              })}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3 min-h-0">
          {activeTab === "overview" && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-3 md:gap-6">
                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-purple-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">إجمالي المنتجات</p>
                      <p className="text-xl md:text-2xl font-bold">{stats.totalProducts}</p>
                    </div>
                    <Package className="w-6 h-6 md:w-8 md:h-8 text-purple-400" />
                  </div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-blue-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">إجمالي المستخدمين</p>
                      <p className="text-xl md:text-2xl font-bold">{stats.totalUsers}</p>
                    </div>
                    <Users className="w-6 h-6 md:w-8 md:h-8 text-blue-400" />
                  </div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-green-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">إجمالي الطلبات</p>
                      <p className="text-xl md:text-2xl font-bold">{stats.totalOrders}</p>
                    </div>
                    <ShoppingCart className="w-6 h-6 md:w-8 md:h-8 text-green-400" />
                  </div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-yellow-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">إجمالي الإيرادات</p>
                      <p className="text-xl md:text-2xl font-bold">{convertAndFormatPrice(stats.totalRevenue)}</p>
                    </div>
                    <DollarSign className="w-6 h-6 md:w-8 md:h-8 text-yellow-400" />
                  </div>
                </div>
              </div>

              {/* Profit Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-6">
                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-emerald-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">إجمالي الأرباح</p>
                      <p className="text-xl md:text-2xl font-bold text-emerald-400">{convertAndFormatPrice(stats.totalProfit)}</p>
                      <p className="text-xs text-gray-500 mt-1">هامش ربح: {stats.profitMargin.toFixed(1)}%</p>
                    </div>
                    <TrendingUp className="w-6 h-6 md:w-8 md:h-8 text-emerald-400" />
                  </div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 md:p-6 border border-gray-700/50 shadow-xl hover:shadow-blue-500/10 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs md:text-sm">أرباح الشهر</p>
                      <p className="text-xl md:text-2xl font-bold text-blue-400">{convertAndFormatPrice(stats.monthlyProfit)}</p>
                      <p className="text-xs text-gray-500 mt-1">من إيرادات: {convertAndFormatPrice(stats.monthlyRevenue)}</p>
                    </div>
                    <BarChart3 className="w-6 h-6 md:w-8 md:h-8 text-blue-400" />
                  </div>
                </div>
              </div>

              {/* Recent Orders */}
              <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
                <div className="p-4 md:p-6 border-b border-gray-700/50">
                  <h2 className="text-lg md:text-xl font-bold">الطلبات الأخيرة</h2>
                </div>
                <div className="p-4 md:p-6">
                  {/* Desktop Table */}
                  <div className="hidden md:block overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="text-right text-gray-400 text-sm">
                          <th className="pb-3">رقم الطلب</th>
                          <th className="pb-3">المنتج</th>
                          <th className="pb-3">المبلغ</th>
                          <th className="pb-3">الحالة</th>
                          <th className="pb-3">التاريخ</th>
                        </tr>
                      </thead>
                      <tbody className="space-y-2">
                        {orders.slice(0, 5).map((order) => {
                          // const product = products.find((p) => p.id === order.productId) // REMOVED: Product management system has been removed
                          return (
                            <tr key={order.id} className="border-t border-gray-700/50">
                              <td className="py-3 font-mono text-sm">#{order.id}</td>
                              <td className="py-3">{"طلب"}</td>
                              <td className="py-3 font-semibold">${order.amount}</td>
                              <td className="py-3">
                                <span
                                  className={`px-2 py-1 rounded-lg text-xs ${
                                    order.status === "completed"
                                      ? "bg-green-400/10 text-green-400"
                                      : order.status === "pending"
                                        ? "bg-yellow-400/10 text-yellow-400"
                                        : "bg-red-400/10 text-red-400"
                                  }`}
                                >
                                  {order.status === "completed"
                                    ? "مكتمل"
                                    : order.status === "pending"
                                      ? "قيد الانتظار"
                                      : "فاشل"}
                                </span>
                              </td>
                              <td className="py-3 text-sm text-gray-400">
                                {new Date(order.created_at).toLocaleDateString("ar")}
                              </td>
                            </tr>
                          )
                        })}
                      </tbody>
                    </table>
                  </div>

                  {/* Mobile Cards */}
                  <div className="md:hidden space-y-3">
                    {orders.slice(0, 5).map((order) => {
                      // const product = products.find((p) => p.id === order.productId) // REMOVED: Product management system has been removed
                      return (
                        <div key={order.id} className="bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                          <div className="flex items-center justify-between mb-3">
                            <span className="font-mono text-sm text-gray-400">#{order.id}</span>
                            <span
                              className={`px-2 py-1 rounded-lg text-xs ${
                                order.status === "completed"
                                  ? "bg-green-400/10 text-green-400"
                                  : order.status === "pending"
                                    ? "bg-yellow-400/10 text-yellow-400"
                                    : "bg-red-400/10 text-red-400"
                              }`}
                            >
                              {order.status === "completed"
                                ? "مكتمل"
                                : order.status === "pending"
                                  ? "قيد الانتظار"
                                  : "فاشل"}
                            </span>
                          </div>
                          <div className="space-y-2">
                            <div>
                              <p className="text-sm text-gray-400">المنتج</p>
                              <p className="font-medium">{"طلب"}</p>
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm text-gray-400">المبلغ</p>
                                <p className="font-semibold text-lg">${order.amount}</p>
                              </div>
                              <div className="text-left">
                                <p className="text-sm text-gray-400">التاريخ</p>
                                <p className="text-sm">{new Date(order.created_at).toLocaleDateString("ar")}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>

              {/* Currency Breakdown - REMOVED: Product management system has been removed */}

              {/* Top Profitable Products - REMOVED: Product management system has been removed */}
            </div>
          )}



          {activeTab === "products" && <ProductManagement initialProducts={dataContextProducts || products} categories={categories} />}

          {activeTab === "categories" && <CategoryManagement />}

          {activeTab === "currencies" && <CurrenciesAdmin />}

          {activeTab === "users" && <LazyUserManagement />}

          {activeTab === "orders" && <LazyOrderManagement />}

          {activeTab === "homepage" && <HomepageManagement products={products} />}
        </div>
      </div>


    </div>
  )
}
