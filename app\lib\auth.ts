import { SupabaseClient } from '@supabase/supabase-js'

// Types for user profile
export interface UserProfile {
  id: string
  email: string
  name: string
  role: 'admin' | 'distributor' | 'user' | 'worker'
  tenant_id: string
  wallet_balance: number
  created_at: string
  updated_at: string
}

// Get current user profile from authenticated session
export async function getCurrentProfile(supabase: SupabaseClient): Promise<UserProfile | null> {
  try {
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return null
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return null
    }

    return profile as UserProfile
  } catch (error) {
    console.error('Error getting current profile:', error)
    return null
  }
}

// Get current user profile with tenant validation
export async function getCurrentProfileWithTenant(
  supabase: SupabaseClient, 
  requiredTenantId?: string
): Promise<UserProfile | null> {
  try {
    const profile = await getCurrentProfile(supabase)
    
    if (!profile) {
      return null
    }

    // If tenant ID is required, validate it matches
    if (requiredTenantId && profile.tenant_id !== requiredTenantId) {
      return null
    }

    return profile
  } catch (error) {
    console.error('Error getting profile with tenant validation:', error)
    return null
  }
}

// No role checking required - always return null (no authentication)
export async function requireRole(
  supabase: SupabaseClient,
  requiredRole: UserProfile['role'] | UserProfile['role'][]
): Promise<UserProfile | null> {
  return null
}

// No admin checking required - always return null (no authentication)
export async function requireAdmin(supabase: SupabaseClient): Promise<UserProfile | null> {
  return null
}

// Get tenant ID from request headers or user profile
export async function getTenantId(
  supabase: SupabaseClient, 
  request?: Request
): Promise<string | null> {
  try {
    // First try to get from request headers (set by middleware)
    if (request) {
      const tenantId = request.headers.get('x-tenant-id')
      if (tenantId) {
        return tenantId
      }
    }

    // Fallback to user profile
    const profile = await getCurrentProfile(supabase)
    return profile?.tenant_id || null
  } catch (error) {
    console.error('Error getting tenant ID:', error)
    return null
  }
}

// Validate user has access to specific tenant
export async function validateTenantAccess(
  supabase: SupabaseClient,
  tenantId: string
): Promise<boolean> {
  try {
    const profile = await getCurrentProfile(supabase)
    return profile?.tenant_id === tenantId
  } catch (error) {
    console.error('Error validating tenant access:', error)
    return false
  }
}
