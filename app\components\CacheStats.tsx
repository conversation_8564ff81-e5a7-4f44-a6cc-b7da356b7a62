"use client"

import { useState, useEffect } from 'react'
import { useData } from '../contexts/DataContext'
import { Database, Zap, Clock, RefreshCw } from 'lucide-react'

export default function CacheStats() {
  const { getCacheStats, refreshCache } = useData()
  const [stats, setStats] = useState<any>(null)
  const [refreshing, setRefreshing] = useState(false)
  const [lastRefresh, setLastRefresh] = useState<string | null>(null)

  useEffect(() => {
    // Update stats every 5 seconds
    const updateStats = () => {
      const currentStats = getCacheStats()
      setStats(currentStats)
    }

    updateStats()
    const interval = setInterval(updateStats, 5000)

    return () => clearInterval(interval)
  }, [getCacheStats])

  const handleRefresh = async () => {
    if (refreshing) return

    setRefreshing(true)
    try {
      await refreshCache()
      setLastRefresh(new Date().toLocaleTimeString())
    } catch (error) {
      console.error('Cache refresh error:', error)
    } finally {
      setRefreshing(false)
    }
  }

  if (!stats) return null

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg p-4 text-xs text-white shadow-lg max-w-xs">
      <div className="flex items-center gap-2 mb-3">
        <Database className="w-4 h-4 text-purple-400" />
        <span className="font-semibold">Cache Performance</span>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between">
          <span className="text-gray-400">Hit Rate:</span>
          <span className={`font-medium ${stats.hitRate > 90 ? 'text-green-400' : stats.hitRate > 70 ? 'text-yellow-400' : 'text-red-400'}`}>
            {stats.hitRate.toFixed(1)}%
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-400">Cache Hits:</span>
          <span className="text-green-400">{stats.hits}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-400">Cache Misses:</span>
          <span className="text-red-400">{stats.misses}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-400">Cached Items:</span>
          <span className="text-blue-400">{stats.size}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-400">Cache Saves:</span>
          <span className="text-purple-400">{stats.saves}</span>
        </div>

        {lastRefresh && (
          <div className="flex justify-between">
            <span className="text-gray-400">Last Refresh:</span>
            <span className="text-gray-300">{lastRefresh}</span>
          </div>
        )}
      </div>

      <button
        onClick={handleRefresh}
        disabled={refreshing}
        className="w-full mt-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-700 disabled:cursor-not-allowed text-white text-xs py-2 px-3 rounded-md transition-colors flex items-center justify-center gap-2"
      >
        {refreshing ? (
          <>
            <RefreshCw className="w-3 h-3 animate-spin" />
            Refreshing...
          </>
        ) : (
          <>
            <RefreshCw className="w-3 h-3" />
            Refresh Cache
          </>
        )}
      </button>
      
      <div className="mt-2 text-center">
        <div className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs ${
          stats.hitRate > 90 
            ? 'bg-green-500/20 text-green-400' 
            : stats.hitRate > 70 
            ? 'bg-yellow-500/20 text-yellow-400' 
            : 'bg-red-500/20 text-red-400'
        }`}>
          <Clock className="w-3 h-3" />
          {stats.hitRate > 90 ? 'Ultra Fast' : stats.hitRate > 70 ? 'Fast' : 'Slow'}
        </div>
      </div>
    </div>
  )
}
