/**
 * Cache System
 * - Uses localStorage only
 * - Fetches only changed data
 * - Zero complexity, maximum performance
 */

interface CacheEntry {
  data: any
  timestamp: number
  lastModified?: string
}

interface CacheStats {
  hits: number
  misses: number
  saves: number
}

class Cache {
  private stats: CacheStats = { hits: 0, misses: 0, saves: 0 }
  private prefix = 'bentakon_'

  /**
   * Get data from localStorage
   */
  get(key: string): any | null {
    try {
      const item = localStorage.getItem(this.prefix + key)
      if (!item) {
        this.stats.misses++
        return null
      }

      const entry: CacheEntry = JSON.parse(item)
      this.stats.hits++
      return entry.data
    } catch (error) {
      console.warn('Cache get error:', error)
      this.stats.misses++
      return null
    }
  }

  /**
   * Save data to localStorage
   */
  set(key: string, data: any, lastModified?: string): void {
    try {
      const entry: CacheEntry = {
        data,
        timestamp: Date.now(),
        lastModified
      }
      
      localStorage.setItem(this.prefix + key, JSON.stringify(entry))
      this.stats.saves++
    } catch (error) {
      console.warn('Cache set error:', error)
      // If storage is full, clear old items and try again
      this.cleanup()
      try {
        localStorage.setItem(this.prefix + key, JSON.stringify({ data, timestamp: Date.now(), lastModified }))
        this.stats.saves++
      } catch (retryError) {
        console.error('Cache storage failed:', retryError)
      }
    }
  }

  /**
   * Check if data exists and is fresh
   */
  has(key: string, maxAge: number = 24 * 60 * 60 * 1000): boolean {
    try {
      const item = localStorage.getItem(this.prefix + key)
      if (!item) return false

      const entry: CacheEntry = JSON.parse(item)
      const age = Date.now() - entry.timestamp
      return age < maxAge
    } catch (error) {
      return false
    }
  }

  /**
   * Get cache timestamp for freshness checking
   */
  getTimestamp(key: string): number | null {
    try {
      const item = localStorage.getItem(this.prefix + key)
      if (!item) return null

      const entry: CacheEntry = JSON.parse(item)
      return entry.timestamp
    } catch (error) {
      return null
    }
  }

  /**
   * Get last modified header for incremental updates
   */
  getLastModified(key: string): string | null {
    try {
      const item = localStorage.getItem(this.prefix + key)
      if (!item) return null

      const entry: CacheEntry = JSON.parse(item)
      return entry.lastModified || null
    } catch (error) {
      return null
    }
  }

  /**
   * Remove item from cache
   */
  remove(key: string): void {
    localStorage.removeItem(this.prefix + key)
  }

  /**
   * Clear all cache items
   */
  clear(): void {
    const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix))
    keys.forEach(key => localStorage.removeItem(key))
  }

  /**
   * Clean up old cache entries
   */
  cleanup(maxAge: number = 7 * 24 * 60 * 60 * 1000): void {
    const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix))
    const now = Date.now()

    keys.forEach(key => {
      try {
        const item = localStorage.getItem(key)
        if (item) {
          const entry: CacheEntry = JSON.parse(item)
          if (now - entry.timestamp > maxAge) {
            localStorage.removeItem(key)
          }
        }
      } catch (error) {
        // Remove corrupted entries
        localStorage.removeItem(key)
      }
    })
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats & { hitRate: number; size: number } {
    const size = Object.keys(localStorage).filter(key => key.startsWith(this.prefix)).length
    const total = this.stats.hits + this.stats.misses
    const hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0

    return {
      ...this.stats,
      hitRate,
      size
    }
  }

  /**
   * Smart fetch - only get data if cache is stale or missing
   */
  async smartFetch<T>(
    key: string,
    fetchFn: () => Promise<T>,
    maxAge: number = 5 * 60 * 1000 // 5 minutes default
  ): Promise<{ data: T; fromCache: boolean }> {
    // Check if we have fresh data
    if (this.has(key, maxAge)) {
      const data = this.get(key)
      return { data, fromCache: true }
    }

    // Fetch fresh data
    const data = await fetchFn()
    this.set(key, data)
    return { data, fromCache: false }
  }

  /**
   * Incremental fetch - only get items that changed since last fetch
   */
  async incrementalFetch<T extends { id: string; updated_at?: string; created_at?: string }>(
    key: string,
    fetchChangedFn: (lastUpdate: string) => Promise<T[]>,
    fetchAllFn: () => Promise<T[]>,
    maxAge: number = 5 * 60 * 1000
  ): Promise<{ data: T[]; fromCache: boolean; changesCount: number }> {
    const lastTimestamp = this.getTimestamp(key)
    const existingData = this.get(key) as T[] || []

    // If no cache or cache is too old, fetch everything
    if (!lastTimestamp || (Date.now() - lastTimestamp > maxAge)) {
      const allData = await fetchAllFn()
      this.set(key, allData)
      return { data: allData, fromCache: false, changesCount: allData.length }
    }

    // Fetch only changes since last update
    const lastUpdateTime = new Date(lastTimestamp).toISOString()
    const changes = await fetchChangedFn(lastUpdateTime)

    if (changes.length === 0) {
      // No changes, return cached data
      return { data: existingData, fromCache: true, changesCount: 0 }
    }

    // Merge changes with existing data
    const mergedData = this.mergeChanges(existingData, changes)
    this.set(key, mergedData)

    return { data: mergedData, fromCache: false, changesCount: changes.length }
  }

  /**
   * Merge changes with existing cached data
   */
  private mergeChanges<T extends { id: string }>(existing: T[], changes: T[]): T[] {
    const existingMap = new Map(existing.map(item => [item.id, item]))

    // Apply changes (updates and new items)
    changes.forEach(change => {
      existingMap.set(change.id, change)
    })

    return Array.from(existingMap.values())
  }

  /**
   * Remove deleted items from cache
   */
  removeFromCache<T extends { id: string }>(key: string, deletedIds: string[]): T[] {
    const existingData = this.get(key) as T[] || []
    const filteredData = existingData.filter(item => !deletedIds.includes(item.id))
    this.set(key, filteredData)
    return filteredData
  }

  /**
   * Advanced incremental fetch with deletion handling
   */
  async incrementalFetchWithDeletes<T extends { id: string; updated_at?: string; deleted_at?: string }>(
    key: string,
    fetchChangedFn: (lastUpdate: string) => Promise<T[]>,
    fetchAllFn: () => Promise<T[]>,
    maxAge: number = 5 * 60 * 1000
  ): Promise<{ data: T[]; fromCache: boolean; changesCount: number; deletedCount: number }> {
    const lastTimestamp = this.getTimestamp(key)
    const existingData = this.get(key) as T[] || []

    // If no cache or cache is too old, fetch everything
    if (!lastTimestamp || (Date.now() - lastTimestamp > maxAge)) {
      const allData = await fetchAllFn()
      // Filter out soft-deleted items
      const activeData = allData.filter(item => !item.deleted_at)
      this.set(key, activeData)
      return { data: activeData, fromCache: false, changesCount: activeData.length, deletedCount: 0 }
    }

    // Fetch changes (including soft-deleted items)
    const lastUpdateTime = new Date(lastTimestamp).toISOString()
    const changes = await fetchChangedFn(lastUpdateTime)

    if (changes.length === 0) {
      return { data: existingData, fromCache: true, changesCount: 0, deletedCount: 0 }
    }

    // Separate active and deleted items
    const activeChanges = changes.filter(item => !item.deleted_at)
    const deletedChanges = changes.filter(item => item.deleted_at)

    // Merge active changes
    const mergedData = this.mergeChanges(existingData, activeChanges)

    // Remove deleted items
    const deletedIds = deletedChanges.map(item => item.id)
    const finalData = mergedData.filter(item => !deletedIds.includes(item.id))

    this.set(key, finalData)

    return {
      data: finalData,
      fromCache: false,
      changesCount: activeChanges.length,
      deletedCount: deletedChanges.length
    }
  }
}

// Create singleton instance
export const cache = new Cache()

// Auto cleanup every hour
if (typeof window !== 'undefined') {
  setInterval(() => {
    cache.cleanup()
  }, 60 * 60 * 1000)
}

/**
 * React hook for caching
 */
export function useCache() {
  return {
    get: cache.get.bind(cache),
    set: cache.set.bind(cache),
    has: cache.has.bind(cache),
    remove: cache.remove.bind(cache),
    clear: cache.clear.bind(cache),
    smartFetch: cache.smartFetch.bind(cache),
    incrementalFetch: cache.incrementalFetch.bind(cache),
    removeFromCache: cache.removeFromCache.bind(cache),
    getStats: cache.getStats.bind(cache)
  }
}

/**
 * Cache keys for different data types
 */
export const CACHE_KEYS = {
  products: (tenantId: string) => `products_${tenantId}`,
  categories: (tenantId: string) => `categories_${tenantId}`,
  orders: (userId: string) => `orders_${userId}`,
  user: (userId: string) => `user_${userId}`,
  banners: (tenantId: string) => `banners_${tenantId}`,
  homepage: (tenantId: string) => `homepage_${tenantId}`,
  tenant: (tenantId: string) => `tenant_${tenantId}`,
  tenantBySlug: (slug: string) => `tenant_slug_${slug}`
}

export default cache
