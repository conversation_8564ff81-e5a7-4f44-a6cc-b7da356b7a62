import { NextRequest } from 'next/server'
import { RateLimitResult, RateLimitConfig } from '../types/admin'

// Rate limiting storage
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

/**
 * Simple rate limiting function for admin APIs
 * @param identifier - Unique identifier (usually IP address)
 * @param limit - Maximum number of requests allowed
 * @param windowMs - Time window in milliseconds
 * @returns boolean indicating if request is allowed
 */
export function rateLimit(
  identifier: string, 
  limit: number = 10, 
  windowMs: number = 60000
): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  const entry = rateLimitMap.get(identifier)!
  
  // Reset if window has expired
  if (now > entry.resetTime) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  // Check if limit exceeded
  if (entry.count >= limit) {
    return false
  }
  
  // Increment count
  entry.count++
  rateLimitMap.set(identifier, entry)
  
  return true
}

/**
 * Enhanced rate limiting with detailed response
 * @param identifier - Unique identifier (usually IP address)
 * @param config - Rate limit configuration
 * @returns RateLimitResult with detailed information
 */
export function rateLimitWithDetails(
  identifier: string,
  config: RateLimitConfig
): RateLimitResult {
  const now = Date.now()
  
  if (!rateLimitMap.has(identifier)) {
    const resetTime = now + config.windowMs
    rateLimitMap.set(identifier, { count: 1, resetTime })
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime
    }
  }
  
  const entry = rateLimitMap.get(identifier)!
  
  // Reset if window has expired
  if (now > entry.resetTime) {
    const resetTime = now + config.windowMs
    rateLimitMap.set(identifier, { count: 1, resetTime })
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime
    }
  }
  
  // Check if limit exceeded
  if (entry.count >= config.maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.resetTime
    }
  }
  
  // Increment count
  entry.count++
  rateLimitMap.set(identifier, entry)
  
  return {
    allowed: true,
    remaining: config.maxRequests - entry.count,
    resetTime: entry.resetTime
  }
}

/**
 * Get client identifier from request
 * @param request - NextRequest object
 * @returns string identifier (IP address or fallback)
 */
export function getClientIdentifier(request: NextRequest): string {
  // Try various headers for IP detection
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const cfConnectingIP = request.headers.get('cf-connecting-ip')

  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  if (realIP) {
    return realIP
  }
  if (cfConnectingIP) {
    return cfConnectingIP
  }

  return request.ip || 'anonymous'
}

/**
 * Clean up expired rate limit entries to prevent memory leaks
 * This should be called periodically
 */
export function cleanupExpiredEntries(): void {
  const now = Date.now()
  
  for (const [key, entry] of rateLimitMap.entries()) {
    if (now > entry.resetTime) {
      rateLimitMap.delete(key)
    }
  }
}

/**
 * Automatic cleanup - runs randomly to prevent memory leaks
 * Call this in your rate limiting functions
 */
export function autoCleanup(): void {
  // Clean up expired entries every ~100 requests (1% chance)
  if (Math.random() < 0.01) {
    cleanupExpiredEntries()
  }
}

// Predefined rate limit configurations for different admin operations
export const RATE_LIMIT_CONFIGS = {
  // General admin operations
  ADMIN_GENERAL: { maxRequests: 20, windowMs: 60000 }, // 20 requests per minute
  
  // User management operations
  ADMIN_USERS: { maxRequests: 10, windowMs: 60000 }, // 10 requests per minute
  
  // Product management operations
  ADMIN_PRODUCTS: { maxRequests: 15, windowMs: 60000 }, // 15 requests per minute
  
  // Currency operations
  ADMIN_CURRENCIES: { maxRequests: 5, windowMs: 60000 }, // 5 requests per minute
  
  // Balance operations (more restrictive)
  ADMIN_BALANCES: { maxRequests: 5, windowMs: 60000 }, // 5 requests per minute
  
  // Earnings/analytics (less restrictive)
  ADMIN_ANALYTICS: { maxRequests: 30, windowMs: 60000 }, // 30 requests per minute
  
  // User creation (very restrictive)
  ADMIN_USER_CREATE: { maxRequests: 3, windowMs: 300000 }, // 3 requests per 5 minutes
} as const

/**
 * Convenience function for admin API rate limiting
 * @param request - NextRequest object
 * @param config - Rate limit configuration (optional, defaults to ADMIN_GENERAL)
 * @returns boolean indicating if request is allowed
 */
export function adminRateLimit(
  request: NextRequest,
  config: RateLimitConfig = RATE_LIMIT_CONFIGS.ADMIN_GENERAL
): boolean {
  autoCleanup()
  const identifier = getClientIdentifier(request)
  return rateLimit(identifier, config.maxRequests, config.windowMs)
}

/**
 * Convenience function for admin API rate limiting with details
 * @param request - NextRequest object
 * @param config - Rate limit configuration (optional, defaults to ADMIN_GENERAL)
 * @returns RateLimitResult with detailed information
 */
export function adminRateLimitWithDetails(
  request: NextRequest,
  config: RateLimitConfig = RATE_LIMIT_CONFIGS.ADMIN_GENERAL
): RateLimitResult {
  autoCleanup()
  const identifier = getClientIdentifier(request)
  return rateLimitWithDetails(identifier, config)
}
