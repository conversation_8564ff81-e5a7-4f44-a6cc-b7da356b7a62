"use client"

import { useState, useEffect } from 'react'
import { Plus, Search, Filter, Edit, Trash2, Package, Eye, Star, X } from 'lucide-react'
import ProductForm from './ProductForm/ProductForm'
import type { Product, Category } from '../../types'

interface ProductManagementProps {
  initialProducts?: Product[]
  categories: Category[]
}

export default function ProductManagement({
  initialProducts = [],
  categories
}: ProductManagementProps) {
  console.log('ProductManagement received initialProducts:', initialProducts?.length)
  const [products, setProducts] = useState<Product[]>(initialProducts)
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(initialProducts)
  const [showForm, setShowForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [featuredFilter, setFeaturedFilter] = useState<'all' | 'featured' | 'regular'>('all')

  // Update products when initialProducts changes
  useEffect(() => {
    console.log('Updating products from initialProducts:', initialProducts?.length)
    setProducts(initialProducts)
    setFilteredProducts(initialProducts)
  }, [initialProducts])

  // Filter products based on search and filters
  useEffect(() => {
    let filtered = products

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Category filter
    if (selectedCategory) {
      filtered = filtered.filter(product => product.category_id === selectedCategory)
    }

    // Featured filter
    if (featuredFilter !== 'all') {
      filtered = filtered.filter(product => 
        featuredFilter === 'featured' ? product.featured : !product.featured
      )
    }

    setFilteredProducts(filtered)
  }, [products, searchTerm, selectedCategory, featuredFilter])

  const fetchProducts = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/products', {
        credentials: 'include' // Include cookies for authentication
      })
      if (response.ok) {
        const data = await response.json()
        setProducts(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateProduct = async (formData: any) => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        await fetchProducts()
        setShowForm(false)
      } else {
        const error = await response.json()
        throw new Error(error.error || 'فشل في إنشاء المنتج')
      }
    } catch (error) {
      console.error('Error creating product:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateProduct = async (formData: any) => {
    if (!editingProduct) return

    setLoading(true)
    try {
      console.log('Attempting to update product:', editingProduct.id)
      console.log('Update payload:', formData)
      console.log('Available cookies:', document.cookie)

      // Transform form data to match API schema
      const apiData = {
        title: formData.title,
        slug: formData.slug,
        description: formData.description,
        category_id: formData.category_id,
        cover_image: formData.cover_image,
        tags: formData.tags,
        featured: formData.featured,
        original_price: formData.original_price,
        user_price: formData.user_price,
        discount_price: formData.discount_price,
        distributor_price: formData.distributor_price,
        manual_quantity: formData.manual_quantity,
        track_inventory: formData.track_inventory,
        unlimited_stock: formData.unlimited_stock,
        has_digital_codes: formData.has_digital_codes
      }

      console.log('Transformed API data:', apiData)

      const response = await fetch(`/api/admin/products/${editingProduct.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify(apiData)
      })

      console.log('Update response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('Update success response:', data)
        await fetchProducts()
        setEditingProduct(null)
        setShowForm(false)
      } else {
        const error = await response.json()
        console.error('Update error response:', error)
        console.error('Error details:', error.details)
        throw new Error(error.error || 'فشل في تحديث المنتج')
      }
    } catch (error) {
      console.error('Error updating product:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteProduct = async (productId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) return

    setLoading(true)
    try {
      const response = await fetch(`/api/admin/products/${productId}`, {
        method: 'DELETE',
        credentials: 'include' // Include cookies for authentication
      })

      if (response.ok) {
        await fetchProducts()
      } else {
        const error = await response.json()
        alert(error.error || 'فشل في حذف المنتج')
      }
    } catch (error) {
      console.error('Error deleting product:', error)
      alert('حدث خطأ أثناء حذف المنتج')
    } finally {
      setLoading(false)
    }
  }

  const handleEditProduct = async (product: Product) => {
    setLoading(true)
    try {
      // Fetch full product details including custom fields and packages

      // Fetch full product details including custom fields and packages
      const response = await fetch(`/api/admin/products/${product.id}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      })


      if (response.ok) {
        const data = await response.json()

        if (data.success) {
          setEditingProduct(data.data)
          setShowForm(true)
        } else {
          throw new Error(data.error || 'فشل في تحميل بيانات المنتج')
        }
      } else {
        const error = await response.json()

        if (response.status === 401) {
          alert('انتهت صلاحية الجلسة. سيتم استخدام البيانات المتاحة. يرجى تسجيل الدخول مرة أخرى للحصول على البيانات الكاملة.')
          // Use existing product data as fallback
          setEditingProduct(product)
          setShowForm(true)
          return
        }
        throw new Error(error.error || 'فشل في تحميل بيانات المنتج')
      }
    } catch (error) {
      console.error('Error fetching product details:', error)
      // Fallback to existing product data
      setEditingProduct(product)
      setShowForm(true)
    } finally {
      setLoading(false)
    }
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingProduct(null)
  }

  if (showForm) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4">
        <div className="bg-gray-900 rounded-xl border border-gray-700 w-full max-w-6xl h-full max-h-[95vh] sm:max-h-[90vh] overflow-hidden shadow-2xl">
          <ProductForm
            product={editingProduct || undefined}
            categories={categories}
            onSubmit={editingProduct ? handleUpdateProduct : handleCreateProduct}
            onCancel={handleCancelForm}
            loading={loading}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">إدارة المنتجات</h1>
          <p className="text-gray-400 mt-1">
            إدارة منتجاتك وحزمها والحقول المخصصة
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowForm(true)}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            إضافة منتج
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="البحث في المنتجات..."
            className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-600 focus:border-purple-500 bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
          />
        </div>

        {/* Category Filter */}
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 rounded-lg border border-gray-600 focus:border-purple-500 bg-gray-700/50 backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
        >
          <option value="">جميع الفئات</option>
          {categories.map((category) => (
            <option key={category.id} value={category.id}>
              {category.name || category.slug}
            </option>
          ))}
        </select>

        {/* Featured Filter */}
        <select
          value={featuredFilter}
          onChange={(e) => setFeaturedFilter(e.target.value as any)}
          className="px-4 py-2 rounded-lg border border-gray-600 focus:border-purple-500 bg-gray-700/50 backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
        >
          <option value="all">جميع المنتجات</option>
          <option value="featured">المنتجات المميزة</option>
          <option value="regular">المنتجات العادية</option>
        </select>

        {/* Results Count */}
        <div className="flex items-center text-sm text-gray-400">
          {filteredProducts.length} من {products.length} منتج
        </div>
      </div>

      {/* Products Grid */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto"></div>
          <p className="text-gray-400 mt-4">جاري التحميل...</p>
        </div>
      ) : filteredProducts.length === 0 ? (
        <div className="text-center py-12 border-2 border-dashed border-gray-600 rounded-lg">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            {products.length === 0 ? 'لا توجد منتجات' : 'لا توجد نتائج'}
          </h3>
          <p className="text-gray-400 mb-4">
            {products.length === 0 
              ? 'ابدأ بإضافة منتجك الأول'
              : 'جرب تغيير معايير البحث أو الفلترة'
            }
          </p>
          {products.length === 0 && (
            <button
              onClick={() => setShowForm(true)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              إضافة منتج
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => (
            <div
              key={product.id}
              className="bg-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-lg overflow-hidden hover:border-purple-500/50 transition-all"
            >
              {/* Product Image */}
              <div className="relative h-48 bg-gray-800">
                {product.cover_image ? (
                  <img
                    src={product.cover_image}
                    alt={product.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Package className="w-16 h-16 text-gray-400" />
                  </div>
                )}
                {product.featured && (
                  <div className="absolute top-2 right-2 bg-yellow-500 text-black px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                    <Star className="w-3 h-3" />
                    مميز
                  </div>
                )}
              </div>

              {/* Product Info */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-lg line-clamp-1">{product.title}</h3>
                  <span className="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded">
                    {categories?.find(cat => cat.id === product.category_id)?.name || 'بدون فئة'}
                  </span>
                </div>
                
                <p className="text-gray-400 text-sm line-clamp-2 mb-3">
                  {product.description}
                </p>

                <div className="flex items-center justify-between mb-4">
                  <div className="text-sm">
                    <span className="text-green-400 font-medium">
                      ${product.user_price}
                    </span>
                    {product.discount_price && (
                      <span className="text-gray-400 line-through mr-2">
                        ${product.discount_price}
                      </span>
                    )}
                  </div>
                  <div className="text-xs text-gray-400">
                    منتج
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleEditProduct(product)}
                    disabled={loading}
                    className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm"
                  >
                    <Edit className="w-3 h-3" />
                    تعديل
                  </button>
                  <button
                    onClick={() => handleDeleteProduct(product.id)}
                    disabled={loading}
                    className="px-3 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
