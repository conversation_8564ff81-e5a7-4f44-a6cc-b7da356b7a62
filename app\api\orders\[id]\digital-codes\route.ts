import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../../lib/supabase/server'
import { z } from 'zod'

// Enhanced security configuration
const RATE_LIMIT_MAX_REQUESTS = 10
const RATE_LIMIT_WINDOW_MS = 60000 // 1 minute
const MAX_CODES_PER_REQUEST = 50

// Enhanced rate limiting with better tracking
const rateLimitStore = new Map<string, { count: number; resetTime: number; firstRequest: number }>()

// Security audit logging interface
interface SecurityAuditLog {
  event_type: 'digital_code_access' | 'rate_limit_exceeded' | 'unauthorized_access' | 'suspicious_activity'
  user_id?: string
  tenant_id?: string
  order_id?: string
  ip_address: string
  user_agent: string
  success: boolean
  error_message?: string
  metadata?: Record<string, any>
  timestamp: string
}

// Enhanced rate limiting function
function checkRateLimit(identifier: string): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now()
  const entry = rateLimitStore.get(identifier)

  // Clean up expired entries
  if (entry && now > entry.resetTime) {
    rateLimitStore.delete(identifier)
  }

  const currentEntry = rateLimitStore.get(identifier)

  if (!currentEntry) {
    // First request in window
    const resetTime = now + RATE_LIMIT_WINDOW_MS
    rateLimitStore.set(identifier, {
      count: 1,
      resetTime,
      firstRequest: now
    })
    return { allowed: true, remaining: RATE_LIMIT_MAX_REQUESTS - 1, resetTime }
  }

  if (currentEntry.count >= RATE_LIMIT_MAX_REQUESTS) {
    return { allowed: false, remaining: 0, resetTime: currentEntry.resetTime }
  }

  // Increment count
  currentEntry.count++
  rateLimitStore.set(identifier, currentEntry)

  return {
    allowed: true,
    remaining: RATE_LIMIT_MAX_REQUESTS - currentEntry.count,
    resetTime: currentEntry.resetTime
  }
}

// Security audit logging function
async function logSecurityEvent(supabase: any, event: SecurityAuditLog) {
  try {
    await supabase
      .from('security_audit_logs')
      .insert({
        ...event,
        tenant_id: event.tenant_id || null
      })
  } catch (error) {
    console.error('Failed to log security event:', error)
  }
}

// Input validation schema
const orderIdSchema = z.string().uuid('Invalid order ID format')

// Enhanced input validation
function validateRequest(orderId: string, userAgent: string, ip: string): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // Validate order ID
  const orderIdResult = orderIdSchema.safeParse(orderId)
  if (!orderIdResult.success) {
    errors.push('Invalid order ID format')
  }

  // Basic security checks
  if (!userAgent || userAgent.length < 10) {
    errors.push('Invalid user agent')
  }

  if (!ip || ip === 'unknown') {
    errors.push('Unable to identify client')
  }

  // Check for suspicious patterns
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /script/i
  ]

  if (suspiciousPatterns.some(pattern => pattern.test(userAgent))) {
    errors.push('Suspicious user agent detected')
  }

  return { valid: errors.length === 0, errors }
}

// GET /api/orders/[id]/digital-codes - Enhanced secure digital codes retrieval
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const startTime = Date.now()

  try {
    const { id: orderId } = await params
    // Extract request metadata for security
    const clientIP = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Enhanced input validation
    const validation = validateRequest(orderId, userAgent, clientIP)
    if (!validation.valid) {
      return NextResponse.json({
        error: 'Invalid request',
        details: validation.errors
      }, { status: 400 })
    }

    // Enhanced rate limiting with IP + User tracking
    const rateLimitKey = `${clientIP}:digital-codes`
    const rateLimit = checkRateLimit(rateLimitKey)

    if (!rateLimit.allowed) {
      return NextResponse.json({
        error: 'Rate limit exceeded',
        retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
      }, {
        status: 429,
        headers: {
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': String(Math.ceil(rateLimit.resetTime / 1000)),
          'Retry-After': String(Math.ceil((rateLimit.resetTime - Date.now()) / 1000))
        }
      })
    }

    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      await logSecurityEvent(supabase, {
        event_type: 'unauthorized_access',
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Authentication failed',
        timestamp: new Date().toISOString()
      })

      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's tenant and role information
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('tenant_id, name, email, role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      await logSecurityEvent(supabase, {
        event_type: 'unauthorized_access',
        user_id: user.id,
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'User profile not found',
        timestamp: new Date().toISOString()
      })

      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Enhanced order verification with detailed security checks
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, user_id, status, tenant_id, product_id, package_id, amount, created_at')
      .eq('id', orderId)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (orderError || !order) {
      await logSecurityEvent(supabase, {
        event_type: 'unauthorized_access',
        user_id: user.id,
        tenant_id: profile.tenant_id,
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Order not found or access denied',
        timestamp: new Date().toISOString()
      })

      return NextResponse.json({ error: 'Order not found' }, { status: 404 })
    }

    // Verify user owns the order (unless admin)
    if (order.user_id !== user.id && profile.role !== 'admin') {
      await logSecurityEvent(supabase, {
        event_type: 'unauthorized_access',
        user_id: user.id,
        tenant_id: profile.tenant_id,
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Order ownership verification failed',
        metadata: {
          order_owner: order.user_id,
          requesting_user: user.id,
          user_role: profile.role
        },
        timestamp: new Date().toISOString()
      })

      return NextResponse.json({
        error: 'Access denied - Order ownership verification failed'
      }, { status: 403 })
    }

    // Only allow access to digital codes for completed orders
    if (order.status !== 'completed') {
      await logSecurityEvent(supabase, {
        event_type: 'unauthorized_access',
        user_id: user.id,
        tenant_id: profile.tenant_id,
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Order not completed',
        metadata: { order_status: order.status },
        timestamp: new Date().toISOString()
      })

      return NextResponse.json({
        error: 'Digital codes only available for completed orders'
      }, { status: 403 })
    }

    // Get digital codes with enhanced security and limits
    const { data: codes, error: codesError } = await supabase
      .from('digital_codes')
      .select(`
        id,
        key_encrypted,
        used,
        viewed_count,
        last_viewed_at,
        assigned_at,
        created_at
      `)
      .eq('assigned_to_order_id', orderId)
      .eq('tenant_id', profile.tenant_id)
      .limit(MAX_CODES_PER_REQUEST)

    if (codesError) {
      await logSecurityEvent(supabase, {
        event_type: 'digital_code_access',
        user_id: user.id,
        tenant_id: profile.tenant_id,
        order_id: orderId,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Database error fetching codes',
        timestamp: new Date().toISOString()
      })

      console.error('Error fetching digital codes:', codesError)
      return NextResponse.json({ error: 'Failed to fetch digital codes' }, { status: 500 })
    }

    // Update viewed count and tracking
    if (codes && codes.length > 0) {
      const codeIds = codes.map(code => code.id)

      // Update view tracking
      for (const codeId of codeIds) {
        await supabase
          .from('digital_codes')
          .update({
            last_viewed_at: new Date().toISOString()
          })
          .eq('id', codeId)
      }
    }

    // Log successful access
    await logSecurityEvent(supabase, {
      event_type: 'digital_code_access',
      user_id: user.id,
      tenant_id: profile.tenant_id,
      order_id: orderId,
      ip_address: clientIP,
      user_agent: userAgent,
      success: true,
      metadata: {
        codes_count: codes?.length || 0,
        user_role: profile.role,
        order_amount: order.amount,
        response_time_ms: Date.now() - startTime
      },
      timestamp: new Date().toISOString()
    })

    // Return codes with security headers
    return NextResponse.json({
      success: true,
      codes: codes || [],
      metadata: {
        order_id: orderId,
        codes_count: codes?.length || 0,
        access_time: new Date().toISOString(),
        order_status: order.status
      }
    }, {
      headers: {
        'X-RateLimit-Remaining': String(rateLimit.remaining),
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'Cache-Control': 'no-store, no-cache, must-revalidate, private'
      }
    })

  } catch (error) {
    // Log unexpected errors
    const supabase = await createClient()
    await logSecurityEvent(supabase, {
      event_type: 'suspicious_activity',
      order_id: orderId,
      ip_address: request.headers.get('x-forwarded-for') || request.ip || 'unknown',
      user_agent: request.headers.get('user-agent') || 'unknown',
      success: false,
      error_message: 'Unexpected server error',
      metadata: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString()
    })

    console.error('Error in GET /api/orders/[id]/digital-codes:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
