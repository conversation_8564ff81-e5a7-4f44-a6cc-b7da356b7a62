"use client"

import { useAuth } from '../contexts/AuthContext'
import { useData } from '../contexts/DataContext'
import { useTenant } from '../contexts/TenantContext'

export default function AdminDebug() {
  const { authState } = useAuth()
  const { currentUser: dataContextUser } = useData()
  const { tenant } = useTenant()

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-md text-xs">
      <h3 className="font-bold mb-2">🔍 Admin Debug Info</h3>
      
      <div className="space-y-2">
        <div>
          <strong>Auth State:</strong>
          <div className="ml-2">
            <div>Loading: {authState.isLoading ? 'Yes' : 'No'}</div>
            <div>Authenticated: {authState.isAuthenticated ? 'Yes' : 'No'}</div>
            <div>User: {authState.user?.email || 'None'}</div>
            <div>Role: {authState.user?.role || 'None'}</div>
          </div>
        </div>

        <div>
          <strong>DataContext User:</strong>
          <div className="ml-2">
            <div>Email: {dataContextUser?.email || 'None'}</div>
            <div>Role: {dataContextUser?.role || 'None'}</div>
          </div>
        </div>

        <div>
          <strong>Tenant:</strong>
          <div className="ml-2">
            <div>ID: {tenant?.id || 'None'}</div>
            <div>Name: {tenant?.name || 'None'}</div>
          </div>
        </div>

        <div>
          <strong>Admin Access:</strong>
          <div className="ml-2">
            <div>Is Admin: {authState.user?.role === 'admin' ? 'Yes' : 'No'}</div>
            <div>Is Worker: {authState.user?.role === 'worker' ? 'Yes' : 'No'}</div>
            <div>Has Access: {(authState.user?.role === 'admin' || authState.user?.role === 'worker') ? 'Yes' : 'No'}</div>
          </div>
        </div>
      </div>

      <div className="mt-2 pt-2 border-t border-gray-600">
        <a 
          href="/admin" 
          className="text-blue-400 hover:text-blue-300 underline"
        >
          Go to Admin Page
        </a>
      </div>
    </div>
  )
}
