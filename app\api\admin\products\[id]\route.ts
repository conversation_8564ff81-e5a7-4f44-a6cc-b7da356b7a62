import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../../lib/supabase/server'
import { rateLimit } from '../../../../lib/rate-limit'
import { 
  productUpdateSchema, 
  generateSlug,
  validatePricingRules 
} from '../../../../lib/products'

// GET /api/admin/products/[id] - Get single product
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return NextResponse.json({ error: 'ملف المستخدم غير موجود' }, { status: 404 })
    }

    // Get product with related data
    const { data: product, error } = await supabase
      .from('products')
      .select(`
        *,
        categories!inner(
          id,
          name,
          slug
        ),
        packages(
          id,
          name,
          description,
          original_price,
          user_price,
          discount_price,
          distributor_price,
          digital_codes,
          image,
          manual_quantity,
          track_inventory,
          unlimited_stock,
          has_digital_codes,
          created_at,
          updated_at
        ),
        custom_fields(
          id,
          label,
          field_type,
          required,
          placeholder,
          description,
          field_order,
          options,
          created_at,
          updated_at
        )
      `)
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (error || !product) {
      return NextResponse.json({ 
        success: false,
        error: 'المنتج غير موجود' 
      }, { status: 404 })
    }

    return NextResponse.json({ 
      success: true,
      data: product 
    })

  } catch (error) {
    console.error('Error in product GET:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}

// PUT /api/admin/products/[id] - Update product
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'طلبات كثيرة جداً' }, { status: 429 })
    }

    const supabase = await createClient()

    // Debug cookies
    const { cookies } = await import('next/headers')
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    const authCookies = allCookies.filter(c => c.name.includes('supabase'))
    console.log('PUT route - Available auth cookies:', authCookies.map(c => ({ name: c.name, hasValue: !!c.value })))

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    console.log('PUT route - Auth check:', { user: user?.id, authError })

    if (!user) {
      console.log('PUT route - No user found, returning 401')
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'غير مصرح - مطلوب صلاحيات إدارية' }, { status: 403 })
    }

    const body = await request.json()
    
    // Validate input
    const validation = productUpdateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        success: false,
        error: 'بيانات غير صالحة',
        details: validation.error.errors.map(e => e.message)
      }, { status: 400 })
    }

    const validatedData = validation.data

    // Auto-generate slug if title is being updated but slug is not provided
    if (validatedData.title && !validatedData.slug) {
      validatedData.slug = generateSlug(validatedData.title)
    }

    // Validate pricing if provided
    if (validatedData.original_price && validatedData.user_price) {
      const pricingValidation = validatePricingRules({
        original_price: validatedData.original_price,
        user_price: validatedData.user_price,
        discount_price: validatedData.discount_price,
        distributor_price: validatedData.distributor_price
      })

      if (!pricingValidation.valid) {
        return NextResponse.json({
          success: false,
          error: 'أخطاء في التسعير',
          details: pricingValidation.errors
        }, { status: 400 })
      }
    }

    // Check if product exists and belongs to tenant
    const { data: existingProduct } = await supabase
      .from('products')
      .select('id, slug')
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!existingProduct) {
      return NextResponse.json({ 
        success: false,
        error: 'المنتج غير موجود' 
      }, { status: 404 })
    }

    // Check for duplicate slug if slug is being updated
    if (validatedData.slug && validatedData.slug !== existingProduct.slug) {
      const { data: duplicateSlug } = await supabase
        .from('products')
        .select('id')
        .eq('tenant_id', profile.tenant_id)
        .eq('slug', validatedData.slug)
        .neq('id', id)
        .single()

      if (duplicateSlug) {
        return NextResponse.json({ 
          success: false,
          error: 'الرابط المختصر موجود بالفعل' 
        }, { status: 409 })
      }
    }

    // Verify category exists if being updated
    if (validatedData.category_id) {
      const { data: category } = await supabase
        .from('categories')
        .select('id')
        .eq('id', validatedData.category_id)
        .eq('tenant_id', profile.tenant_id)
        .single()

      if (!category) {
        return NextResponse.json({ 
          success: false,
          error: 'الفئة المحددة غير موجودة' 
        }, { status: 400 })
      }
    }

    // Update product
    const { data: product, error } = await supabase
      .from('products')
      .update({
        ...validatedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .select()
      .single()

    if (error) {
      console.error('Error updating product:', error)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في تحديث المنتج' 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      data: product 
    })

  } catch (error) {
    console.error('Error in product PUT:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}

// DELETE /api/admin/products/[id] - Delete product
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'طلبات كثيرة جداً' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'غير مصرح - مطلوب صلاحيات إدارية' }, { status: 403 })
    }

    // Check if product exists and belongs to tenant
    const { data: product } = await supabase
      .from('products')
      .select('id')
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!product) {
      return NextResponse.json({ 
        success: false,
        error: 'المنتج غير موجود' 
      }, { status: 404 })
    }

    // Check if product has orders
    const { data: orders } = await supabase
      .from('orders')
      .select('id')
      .eq('product_id', id)
      .eq('tenant_id', profile.tenant_id)
      .limit(1)

    if (orders && orders.length > 0) {
      return NextResponse.json({ 
        success: false,
        error: 'لا يمكن حذف المنتج لأنه يحتوي على طلبات' 
      }, { status: 400 })
    }

    // Delete related data first (packages, custom fields, digital codes)
    // Note: These should cascade delete with proper foreign key constraints
    await supabase
      .from('custom_fields')
      .delete()
      .eq('product_id', id)
      .eq('tenant_id', profile.tenant_id)

    // Get package IDs first
    const { data: packageIds } = await supabase
      .from('packages')
      .select('id')
      .eq('product_id', id)
      .eq('tenant_id', profile.tenant_id)

    if (packageIds && packageIds.length > 0) {
      await supabase
        .from('digital_codes')
        .delete()
        .in('package_id', packageIds.map(p => p.id))
    }

    await supabase
      .from('packages')
      .delete()
      .eq('product_id', id)
      .eq('tenant_id', profile.tenant_id)

    // Delete product
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)

    if (error) {
      console.error('Error deleting product:', error)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في حذف المنتج' 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      message: 'تم حذف المنتج بنجاح' 
    })

  } catch (error) {
    console.error('Error in product DELETE:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}
